<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* video/show.html.twig */
class __TwigTemplate_ff53fff703f82da28fc34ce1d34c40a2 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'meta_description' => [$this, 'block_meta_description'],
            'stylesheets' => [$this, 'block_stylesheets'],
            'body' => [$this, 'block_body'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "video/show.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "video/show.html.twig"));

        $this->parent = $this->load("base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 3, $this->source); })()), "title", [], "any", false, false, false, 3), "html", null, true);
        yield " - Capitol Academy";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_meta_description(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "meta_description"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "meta_description"));

        yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 5, $this->source); })()), "description", [], "any", false, false, false, 5)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::slice($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 5, $this->source); })()), "description", [], "any", false, false, false, 5), 0, 160), "html", null, true)) : ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((("Watch " . CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 5, $this->source); })()), "title", [], "any", false, false, false, 5)) . " on Capitol Academy - Professional Trading Education"), "html", null, true)));
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_stylesheets(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        // line 8
        yield "    ";
        yield from $this->yieldParentBlock("stylesheets", $context, $blocks);
        yield "
    <style>
        /* Capitol Academy Video Player Styles */
        .video-hero-section {
            background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%);
            min-height: 100vh;
            padding-top: 80px;
            position: relative;
            overflow: hidden;
        }

        .video-hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('/images/trading-bg-pattern.png') repeat;
            opacity: 0.05;
            z-index: 1;
        }

        .video-container {
            position: relative;
            z-index: 2;
        }

        .video-player-wrapper {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
        }

        .video-player-wrapper:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
        }

        .video-player {
            width: 100%;
            height: 500px;
            border-radius: 15px;
            overflow: hidden;
            background: #000;
            position: relative;
        }

        .video-player iframe,
        .video-player video {
            width: 100%;
            height: 100%;
            border: none;
            border-radius: 15px;
        }

        .video-info-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            margin-top: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .video-title {
            color: #011a2d;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            font-family: 'Montserrat', sans-serif;
        }

        .video-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 25px;
            align-items: center;
        }

        .video-badge {
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .badge-free {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }

        .badge-premium {
            background: linear-gradient(135deg, #ffc107, #e0a800);
            color: #000;
        }

        .badge-login-required {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
        }

        .video-description {
            color: #343a40;
            font-size: 1.1rem;
            line-height: 1.8;
            margin-bottom: 30px;
        }

        .access-denied-card {
            background: linear-gradient(135deg, #a90418, #8b0314);
            color: white;
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            margin-top: 30px;
        }

        .access-denied-card h3 {
            font-size: 2rem;
            margin-bottom: 20px;
        }

        .purchase-button {
            background: linear-gradient(135deg, #a90418, #8b0314);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .purchase-button:hover {
            background: linear-gradient(135deg, #8b0314, #a90418);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(169, 4, 24, 0.3);
            color: white;
            text-decoration: none;
        }

        .login-button {
            background: linear-gradient(135deg, #011a2d, #1a3461);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .login-button:hover {
            background: linear-gradient(135deg, #1a3461, #011a2d);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(1, 26, 45, 0.3);
            color: white;
            text-decoration: none;
        }

        .premium-video-info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-top: 20px;
        }

        .price-info {
            text-align: center;
        }

        .price-display {
            font-size: 2.5rem;
            font-weight: 700;
            color: #a90418;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .access-info {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1.1rem;
            margin-bottom: 0;
        }

        .add-to-cart-button {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-weight: 600;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            width: 100%;
            justify-content: center;
        }

        .add-to-cart-button:hover {
            background: linear-gradient(135deg, #20c997, #28a745);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(40, 167, 69, 0.3);
            color: white;
        }

        .add-to-cart-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .related-videos-section {
            background: #f8f9fa;
            padding: 60px 0;
        }

        .related-video-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            height: 100%;
        }

        .related-video-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .related-video-thumbnail {
            width: 100%;
            height: 200px;
            object-fit: cover;
            background: #f8f9fa;
        }

        .related-video-content {
            padding: 20px;
        }

        .related-video-title {
            color: #011a2d;
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 10px;
            line-height: 1.4;
        }

        .vdocipher-player {
            background: #000;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 500px;
            position: relative;
        }

        .vdocipher-loading {
            color: white;
            text-align: center;
        }

        .video-source-indicator {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        @media (max-width: 768px) {
            .video-hero-section {
                padding-top: 60px;
                min-height: auto;
            }

            .video-player {
                height: 300px;
            }

            .video-title {
                font-size: 2rem;
            }

            .video-meta {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }
    </style>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 331
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_body(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        // line 332
        yield "<!-- Video Hero Section -->
<section class=\"video-hero-section\">
    <div class=\"container\">
        <div class=\"row justify-content-center\">
            <div class=\"col-lg-10\">
                <div class=\"video-container\">
                    <!-- Video Player Wrapper -->
                    <div class=\"video-player-wrapper\">
                        <div class=\"video-player\">
                            ";
        // line 341
        if ((($tmp = (isset($context["has_access"]) || array_key_exists("has_access", $context) ? $context["has_access"] : (function () { throw new RuntimeError('Variable "has_access" does not exist.', 341, $this->source); })())) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 342
            yield "                                <!-- Video Source Indicator -->
                                <div class=\"video-source-indicator\">
                                    ";
            // line 344
            if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 344, $this->source); })()), "videoSourceType", [], "any", false, false, false, 344) == "youtube")) {
                // line 345
                yield "                                        <i class=\"fab fa-youtube\"></i> YouTube
                                    ";
            } elseif ((CoreExtension::getAttribute($this->env, $this->source,             // line 346
(isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 346, $this->source); })()), "videoSourceType", [], "any", false, false, false, 346) == "vdocipher")) {
                // line 347
                yield "                                        <i class=\"fas fa-shield-alt\"></i> VdoCipher
                                    ";
            } else {
                // line 349
                yield "                                        <i class=\"fas fa-play\"></i> Direct
                                    ";
            }
            // line 351
            yield "                                </div>

                                <!-- YouTube Video Player -->
                                ";
            // line 354
            if (((CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 354, $this->source); })()), "videoSourceType", [], "any", false, false, false, 354) == "youtube") && CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 354, $this->source); })()), "youtubeEmbedUrl", [], "any", false, false, false, 354))) {
                // line 355
                yield "                                    <iframe src=\"";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 355, $this->source); })()), "youtubeEmbedUrl", [], "any", false, false, false, 355), "html", null, true);
                yield "\"
                                            frameborder=\"0\"
                                            allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"
                                            allowfullscreen>
                                    </iframe>

                                <!-- VdoCipher Video Player -->
                                ";
            } elseif (((CoreExtension::getAttribute($this->env, $this->source,             // line 362
(isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 362, $this->source); })()), "videoSourceType", [], "any", false, false, false, 362) == "vdocipher") && CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 362, $this->source); })()), "vdocipherVideoId", [], "any", false, false, false, 362))) {
                // line 363
                yield "                                    <div class=\"vdocipher-player\" id=\"vdocipher-player\">
                                        <div class=\"vdocipher-loading\">
                                            <i class=\"fas fa-spinner fa-spin\" style=\"font-size: 3rem; margin-bottom: 1rem;\"></i>
                                            <h4>Loading Secure Video...</h4>
                                            <p>Initializing DRM-protected playback</p>
                                        </div>
                                    </div>

                                <!-- Direct Upload Video Player -->
                                ";
            } elseif ((($tmp = CoreExtension::getAttribute($this->env, $this->source,             // line 372
(isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 372, $this->source); })()), "videoFile", [], "any", false, false, false, 372)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                // line 373
                yield "                                    <video controls preload=\"metadata\" poster=\"";
                yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 373, $this->source); })()), "thumbnail", [], "any", false, false, false, 373)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl(("uploads/videos/thumbnails/" . CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 373, $this->source); })()), "thumbnail", [], "any", false, false, false, 373))), "html", null, true)) : (""));
                yield "\">
                                        <source src=\"";
                // line 374
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl(("uploads/videos/files/" . CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 374, $this->source); })()), "videoFile", [], "any", false, false, false, 374))), "html", null, true);
                yield "\" type=\"video/mp4\">
                                        <p>Your browser doesn't support HTML5 video. <a href=\"";
                // line 375
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl(("uploads/videos/files/" . CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 375, $this->source); })()), "videoFile", [], "any", false, false, false, 375))), "html", null, true);
                yield "\">Download the video</a> instead.</p>
                                    </video>

                                <!-- No Video Source -->
                                ";
            } else {
                // line 380
                yield "                                    <div class=\"d-flex align-items-center justify-content-center h-100\">
                                        <div class=\"text-center text-white\">
                                            <i class=\"fas fa-exclamation-triangle\" style=\"font-size: 3rem; margin-bottom: 1rem;\"></i>
                                            <h4>Video Not Available</h4>
                                            <p>This video is currently being processed. Please try again later.</p>
                                        </div>
                                    </div>
                                ";
            }
            // line 388
            yield "                            ";
        } else {
            // line 389
            yield "                                <!-- Access Denied - Show Thumbnail with Overlay -->
                                <div class=\"d-flex align-items-center justify-content-center h-100\" style=\"background: ";
            // line 390
            yield (((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 390, $this->source); })()), "thumbnail", [], "any", false, false, false, 390)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((("url(" . $this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl(("uploads/videos/thumbnails/" . CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 390, $this->source); })()), "thumbnail", [], "any", false, false, false, 390)))) . ")"), "html", null, true)) : ("#000"));
            yield " center/cover;\">
                                    <div style=\"background: rgba(0, 0, 0, 0.8); width: 100%; height: 100%; display: flex; align-items: center; justify-content: center;\">
                                        <div class=\"text-center text-white\">
                                            <i class=\"fas fa-lock\" style=\"font-size: 4rem; margin-bottom: 1rem;\"></i>
                                            <h3>Premium Content</h3>
                                            <p class=\"mb-0\">This video requires access to view</p>
                                        </div>
                                    </div>
                                </div>
                            ";
        }
        // line 400
        yield "                        </div>
                    </div>

                    <!-- Video Information Card -->
                    <div class=\"video-info-card\">
                        <h1 class=\"video-title\">";
        // line 405
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 405, $this->source); })()), "title", [], "any", false, false, false, 405), "html", null, true);
        yield "</h1>

                        <!-- Video Meta Information -->
                        <div class=\"video-meta\">
                            <!-- Access Level Badge -->
                            ";
        // line 410
        if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 410, $this->source); })()), "accessLevel", [], "any", false, false, false, 410) == "public_free")) {
            // line 411
            yield "                                <span class=\"video-badge badge-free\">
                                    <i class=\"fas fa-globe\"></i>
                                    Public Free
                                </span>
                            ";
        } elseif ((CoreExtension::getAttribute($this->env, $this->source,         // line 415
(isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 415, $this->source); })()), "accessLevel", [], "any", false, false, false, 415) == "login_required_free")) {
            // line 416
            yield "                                <span class=\"video-badge badge-login-required\">
                                    <i class=\"fas fa-user\"></i>
                                    Login Required
                                </span>
                            ";
        } else {
            // line 421
            yield "                                <span class=\"video-badge badge-premium\">
                                    <i class=\"fas fa-crown\"></i>
                                    Premium - ";
            // line 423
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 423, $this->source); })()), "formattedPrice", [], "any", false, false, false, 423), "html", null, true);
            yield "
                                    ";
            // line 424
            if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 424, $this->source); })()), "accessDuration", [], "any", false, false, false, 424)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                // line 425
                yield "                                        (";
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 425, $this->source); })()), "formattedAccessDuration", [], "any", false, false, false, 425), "html", null, true);
                yield ")
                                    ";
            }
            // line 427
            yield "                                </span>
                            ";
        }
        // line 429
        yield "
                            <!-- Category Badge -->
                            ";
        // line 431
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 431, $this->source); })()), "category", [], "any", false, false, false, 431)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 432
            yield "                                <span class=\"video-badge\" style=\"background: #6c757d; color: white;\">
                                    <i class=\"fas fa-tag\"></i>
                                    ";
            // line 434
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 434, $this->source); })()), "category", [], "any", false, false, false, 434), "html", null, true);
            yield "
                                </span>
                            ";
        }
        // line 437
        yield "
                            <!-- Video Source Badge -->
                            <span class=\"video-badge\" style=\"background: #17a2b8; color: white;\">
                                ";
        // line 440
        if ((CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 440, $this->source); })()), "videoSourceType", [], "any", false, false, false, 440) == "youtube")) {
            // line 441
            yield "                                    <i class=\"fab fa-youtube\"></i>
                                    YouTube
                                ";
        } elseif ((CoreExtension::getAttribute($this->env, $this->source,         // line 443
(isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 443, $this->source); })()), "videoSourceType", [], "any", false, false, false, 443) == "vdocipher")) {
            // line 444
            yield "                                    <i class=\"fas fa-shield-alt\"></i>
                                    VdoCipher
                                ";
        } else {
            // line 447
            yield "                                    <i class=\"fas fa-upload\"></i>
                                    Direct Upload
                                ";
        }
        // line 450
        yield "                            </span>

                            <!-- Upload Date -->
                            <span class=\"video-badge\" style=\"background: #f8f9fa; color: #6c757d; border: 1px solid #dee2e6;\">
                                <i class=\"fas fa-calendar\"></i>
                                ";
        // line 455
        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 455, $this->source); })()), "createdAt", [], "any", false, false, false, 455), "M d, Y"), "html", null, true);
        yield "
                            </span>
                        </div>

                        <!-- Video Description -->
                        ";
        // line 460
        if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 460, $this->source); })()), "description", [], "any", false, false, false, 460)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 461
            yield "                            <div class=\"video-description\">
                                ";
            // line 462
            yield Twig\Extension\CoreExtension::nl2br($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 462, $this->source); })()), "description", [], "any", false, false, false, 462), "html", null, true));
            yield "
                            </div>
                        ";
        }
        // line 465
        yield "
                        <!-- Access Control Actions -->
                        ";
        // line 467
        if ((($tmp =  !(isset($context["has_access"]) || array_key_exists("has_access", $context) ? $context["has_access"] : (function () { throw new RuntimeError('Variable "has_access" does not exist.', 467, $this->source); })())) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 468
            yield "                            ";
            if (((CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 468, $this->source); })()), "accessLevel", [], "any", false, false, false, 468) == "login_required_free") &&  !CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 468, $this->source); })()), "user", [], "any", false, false, false, 468))) {
                // line 469
                yield "                                <!-- Login Required for Free Video -->
                                <div class=\"text-center\">
                                    <p class=\"mb-3\">Please log in to watch this free video.</p>
                                    <a href=\"";
                // line 472
                yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_login");
                yield "\" class=\"login-button\">
                                        <i class=\"fas fa-sign-in-alt\"></i>
                                        Login to Watch
                                    </a>
                                </div>
                            ";
            } elseif ((CoreExtension::getAttribute($this->env, $this->source,             // line 477
(isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 477, $this->source); })()), "accessLevel", [], "any", false, false, false, 477) == "premium")) {
                // line 478
                yield "                                <!-- Premium Video Purchase -->
                                <div class=\"text-center\">
                                    ";
                // line 480
                if ((($tmp =  !CoreExtension::getAttribute($this->env, $this->source, (isset($context["app"]) || array_key_exists("app", $context) ? $context["app"] : (function () { throw new RuntimeError('Variable "app" does not exist.', 480, $this->source); })()), "user", [], "any", false, false, false, 480)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    // line 481
                    yield "                                        <p class=\"mb-3\">Please log in to purchase this premium video.</p>
                                        <a href=\"";
                    // line 482
                    yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_login");
                    yield "\" class=\"login-button me-3\">
                                            <i class=\"fas fa-sign-in-alt\"></i>
                                            Login
                                        </a>
                                    ";
                } else {
                    // line 487
                    yield "                                        <div class=\"premium-video-info\">
                                            <div class=\"price-info mb-3\">
                                                <h4 class=\"price-display\">";
                    // line 489
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 489, $this->source); })()), "formattedPrice", [], "any", false, false, false, 489), "html", null, true);
                    yield "</h4>
                                                <p class=\"access-info\">
                                                    ";
                    // line 491
                    if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 491, $this->source); })()), "accessDuration", [], "any", false, false, false, 491)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                        // line 492
                        yield "                                                        ";
                        yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 492, $this->source); })()), "accessDuration", [], "any", false, false, false, 492), "html", null, true);
                        yield " days access
                                                    ";
                    } else {
                        // line 494
                        yield "                                                        Lifetime access
                                                    ";
                    }
                    // line 496
                    yield "                                                </p>
                                            </div>
                                            <button class=\"add-to-cart-button\" onclick=\"addToCart('video', ";
                    // line 498
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 498, $this->source); })()), "id", [], "any", false, false, false, 498), "html", null, true);
                    yield ")\">
                                                <i class=\"fas fa-cart-plus\"></i>
                                                Add to Cart
                                            </button>
                                        </div>
                                    ";
                }
                // line 504
                yield "                                </div>
                            ";
            }
            // line 506
            yield "                        ";
        }
        // line 507
        yield "                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Videos Section -->
";
        // line 515
        if ((Twig\Extension\CoreExtension::length($this->env->getCharset(), (isset($context["related_videos"]) || array_key_exists("related_videos", $context) ? $context["related_videos"] : (function () { throw new RuntimeError('Variable "related_videos" does not exist.', 515, $this->source); })())) > 0)) {
            // line 516
            yield "<section class=\"related-videos-section\">
    <div class=\"container\">
        <div class=\"row\">
            <div class=\"col-12\">
                <h2 class=\"text-center mb-5\" style=\"color: #011a2d; font-family: 'Montserrat', sans-serif; font-weight: 700;\">
                    Related Videos
                </h2>
            </div>
        </div>
        <div class=\"row g-4\">
            ";
            // line 526
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable((isset($context["related_videos"]) || array_key_exists("related_videos", $context) ? $context["related_videos"] : (function () { throw new RuntimeError('Variable "related_videos" does not exist.', 526, $this->source); })()));
            foreach ($context['_seq'] as $context["_key"] => $context["related_video"]) {
                // line 527
                yield "                <div class=\"col-lg-3 col-md-6\">
                    <div class=\"related-video-card\">
                        <a href=\"";
                // line 529
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_video_show", ["id" => CoreExtension::getAttribute($this->env, $this->source, $context["related_video"], "id", [], "any", false, false, false, 529)]), "html", null, true);
                yield "\" class=\"text-decoration-none\">
                            ";
                // line 530
                if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["related_video"], "thumbnail", [], "any", false, false, false, 530)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    // line 531
                    yield "                                <img src=\"";
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl(("uploads/videos/thumbnails/" . CoreExtension::getAttribute($this->env, $this->source, $context["related_video"], "thumbnail", [], "any", false, false, false, 531))), "html", null, true);
                    yield "\"
                                     alt=\"";
                    // line 532
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["related_video"], "title", [], "any", false, false, false, 532), "html", null, true);
                    yield "\"
                                     class=\"related-video-thumbnail\">
                            ";
                } else {
                    // line 535
                    yield "                                <div class=\"related-video-thumbnail d-flex align-items-center justify-content-center\" style=\"background: #f8f9fa;\">
                                    <i class=\"fas fa-video text-muted\" style=\"font-size: 3rem;\"></i>
                                </div>
                            ";
                }
                // line 539
                yield "                            <div class=\"related-video-content\">
                                <h5 class=\"related-video-title\">";
                // line 540
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["related_video"], "title", [], "any", false, false, false, 540), "html", null, true);
                yield "</h5>
                                <div class=\"d-flex justify-content-between align-items-center\">
                                    <small class=\"text-muted\">
                                        <i class=\"fas fa-calendar me-1\"></i>
                                        ";
                // line 544
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, $context["related_video"], "createdAt", [], "any", false, false, false, 544), "M d, Y"), "html", null, true);
                yield "
                                    </small>
                                    ";
                // line 546
                if ((CoreExtension::getAttribute($this->env, $this->source, $context["related_video"], "accessLevel", [], "any", false, false, false, 546) == "public_free")) {
                    // line 547
                    yield "                                        <span class=\"badge bg-success\">Free</span>
                                    ";
                } elseif ((CoreExtension::getAttribute($this->env, $this->source,                 // line 548
$context["related_video"], "accessLevel", [], "any", false, false, false, 548) == "login_required_free")) {
                    // line 549
                    yield "                                        <span class=\"badge bg-info\">Login Required</span>
                                    ";
                } else {
                    // line 551
                    yield "                                        <span class=\"badge bg-warning text-dark\">";
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["related_video"], "formattedPrice", [], "any", false, false, false, 551), "html", null, true);
                    yield "</span>
                                    ";
                }
                // line 553
                yield "                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['related_video'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 559
            yield "        </div>
    </div>
</section>
";
        }
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 565
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 566
        yield "    ";
        yield from $this->yieldParentBlock("javascripts", $context, $blocks);
        yield "
    <script>
        // VdoCipher Player Initialization
        ";
        // line 569
        if ((((isset($context["has_access"]) || array_key_exists("has_access", $context) ? $context["has_access"] : (function () { throw new RuntimeError('Variable "has_access" does not exist.', 569, $this->source); })()) && (CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 569, $this->source); })()), "videoSourceType", [], "any", false, false, false, 569) == "vdocipher")) && CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 569, $this->source); })()), "vdocipherVideoId", [], "any", false, false, false, 569))) {
            // line 570
            yield "        document.addEventListener('DOMContentLoaded', function() {
            initializeVdoCipherPlayer();
        });

        function initializeVdoCipherPlayer() {
            // Get video access token from server
            fetch('";
            // line 576
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_video_vdocipher_token", ["id" => CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 576, $this->source); })()), "id", [], "any", false, false, false, 576)]), "html", null, true);
            yield "', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.otp && data.playbackInfo) {
                    // Initialize VdoCipher player
                    new VdoPlayer({
                        otp: data.otp,
                        playbackInfo: data.playbackInfo,
                        theme: \"9bb535\",
                        container: document.querySelector(\"#vdocipher-player\"),
                        width: \"100%\",
                        height: \"100%\"
                    });
                } else {
                    showVdoCipherError('Failed to load video. Please try again.');
                }
            })
            .catch(error => {
                console.error('VdoCipher initialization error:', error);
                showVdoCipherError('Unable to load video player. Please refresh the page.');
            });
        }

        function showVdoCipherError(message) {
            const player = document.getElementById('vdocipher-player');
            player.innerHTML = `
                <div class=\"vdocipher-loading\">
                    <i class=\"fas fa-exclamation-triangle text-warning\" style=\"font-size: 3rem; margin-bottom: 1rem;\"></i>
                    <h4>Video Loading Error</h4>
                    <p>\${message}</p>
                    <button class=\"btn btn-primary mt-3\" onclick=\"initializeVdoCipherPlayer()\">
                        <i class=\"fas fa-redo\"></i> Retry
                    </button>
                </div>
            `;
        }
        ";
        }
        // line 619
        yield "
        // Video Purchase Function
        function purchaseVideo(videoId) {
            // Show loading state
            const button = event.target.closest('.purchase-button');
            const originalContent = button.innerHTML;
            button.innerHTML = '<i class=\"fas fa-spinner fa-spin\"></i> Processing...';
            button.disabled = true;

            // Create Stripe checkout session
            fetch('";
        // line 629
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_video_purchase");
        yield "', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    video_id: videoId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.checkout_url) {
                    // Redirect to Stripe checkout
                    window.location.href = data.checkout_url;
                } else {
                    // Show error message
                    alert(data.message || 'Unable to process purchase. Please try again.');
                    button.innerHTML = originalContent;
                    button.disabled = false;
                }
            })
            .catch(error => {
                console.error('Purchase error:', error);
                alert('Unable to process purchase. Please try again.');
                button.innerHTML = originalContent;
                button.disabled = false;
            });
        }

        // Video Analytics Tracking
        ";
        // line 660
        if ((($tmp = (isset($context["has_access"]) || array_key_exists("has_access", $context) ? $context["has_access"] : (function () { throw new RuntimeError('Variable "has_access" does not exist.', 660, $this->source); })())) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
            // line 661
            yield "        document.addEventListener('DOMContentLoaded', function() {
            // Track video view
            fetch('";
            // line 663
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_video_track_view", ["slug" => CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 663, $this->source); })()), "slug", [], "any", false, false, false, 663)]), "html", null, true);
            yield "', {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            // Track video engagement
            let watchTime = 0;
            const trackingInterval = setInterval(() => {
                watchTime += 5; // Track every 5 seconds

                // Send tracking data every 30 seconds
                if (watchTime % 30 === 0) {
                    fetch('";
            // line 677
            yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_video_track_engagement", ["slug" => CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 677, $this->source); })()), "slug", [], "any", false, false, false, 677)]), "html", null, true);
            yield "', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: JSON.stringify({
                            watch_time: watchTime
                        })
                    });
                }
            }, 5000);

            // Stop tracking when user leaves page
            window.addEventListener('beforeunload', () => {
                clearInterval(trackingInterval);
            });
        });
        ";
        }
        // line 696
        yield "
        // Smooth scroll for related videos
        document.querySelectorAll('a[href^=\"#\"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add to Cart Function
        function addToCart(type, id) {
            const button = event.target.closest('.add-to-cart-button');
            const originalContent = button.innerHTML;

            // Show loading state
            button.innerHTML = '<i class=\"fas fa-spinner fa-spin\"></i> Adding...';
            button.disabled = true;

            fetch('";
        // line 720
        yield $this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_cart_add");
        yield "', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: `type=\${type}&id=\${id}&quantity=1`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    button.innerHTML = '<i class=\"fas fa-check\"></i> Added to Cart!';
                    button.style.background = 'linear-gradient(135deg, #28a745, #20c997)';

                    // Update cart widget if it exists
                    if (typeof updateCartWidget === 'function') {
                        updateCartWidget();
                    }

                    // Reset button after 2 seconds
                    setTimeout(() => {
                        button.innerHTML = originalContent;
                        button.disabled = false;
                        button.style.background = '';
                    }, 2000);
                } else {
                    // Show error message
                    alert(data.message || 'Unable to add item to cart. Please try again.');
                    button.innerHTML = originalContent;
                    button.disabled = false;
                }
            })
            .catch(error => {
                console.error('Add to cart error:', error);
                alert('Unable to add item to cart. Please try again.');
                button.innerHTML = originalContent;
                button.disabled = false;
            });
        }
    </script>

    <!-- VdoCipher Player Script -->
    ";
        // line 763
        if (((isset($context["has_access"]) || array_key_exists("has_access", $context) ? $context["has_access"] : (function () { throw new RuntimeError('Variable "has_access" does not exist.', 763, $this->source); })()) && (CoreExtension::getAttribute($this->env, $this->source, (isset($context["video"]) || array_key_exists("video", $context) ? $context["video"] : (function () { throw new RuntimeError('Variable "video" does not exist.', 763, $this->source); })()), "videoSourceType", [], "any", false, false, false, 763) == "vdocipher"))) {
            // line 764
            yield "    <script src=\"https://player.vdocipher.com/v2/api.js\"></script>
    ";
        }
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "video/show.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  1138 => 764,  1136 => 763,  1090 => 720,  1064 => 696,  1042 => 677,  1025 => 663,  1021 => 661,  1019 => 660,  985 => 629,  973 => 619,  927 => 576,  919 => 570,  917 => 569,  910 => 566,  897 => 565,  882 => 559,  871 => 553,  865 => 551,  861 => 549,  859 => 548,  856 => 547,  854 => 546,  849 => 544,  842 => 540,  839 => 539,  833 => 535,  827 => 532,  822 => 531,  820 => 530,  816 => 529,  812 => 527,  808 => 526,  796 => 516,  794 => 515,  784 => 507,  781 => 506,  777 => 504,  768 => 498,  764 => 496,  760 => 494,  754 => 492,  752 => 491,  747 => 489,  743 => 487,  735 => 482,  732 => 481,  730 => 480,  726 => 478,  724 => 477,  716 => 472,  711 => 469,  708 => 468,  706 => 467,  702 => 465,  696 => 462,  693 => 461,  691 => 460,  683 => 455,  676 => 450,  671 => 447,  666 => 444,  664 => 443,  660 => 441,  658 => 440,  653 => 437,  647 => 434,  643 => 432,  641 => 431,  637 => 429,  633 => 427,  627 => 425,  625 => 424,  621 => 423,  617 => 421,  610 => 416,  608 => 415,  602 => 411,  600 => 410,  592 => 405,  585 => 400,  572 => 390,  569 => 389,  566 => 388,  556 => 380,  548 => 375,  544 => 374,  539 => 373,  537 => 372,  526 => 363,  524 => 362,  513 => 355,  511 => 354,  506 => 351,  502 => 349,  498 => 347,  496 => 346,  493 => 345,  491 => 344,  487 => 342,  485 => 341,  474 => 332,  461 => 331,  127 => 8,  114 => 7,  91 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'base.html.twig' %}

{% block title %}{{ video.title }} - Capitol Academy{% endblock %}

{% block meta_description %}{{ video.description ? video.description|slice(0, 160) : 'Watch ' ~ video.title ~ ' on Capitol Academy - Professional Trading Education' }}{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        /* Capitol Academy Video Player Styles */
        .video-hero-section {
            background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%);
            min-height: 100vh;
            padding-top: 80px;
            position: relative;
            overflow: hidden;
        }

        .video-hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('/images/trading-bg-pattern.png') repeat;
            opacity: 0.05;
            z-index: 1;
        }

        .video-container {
            position: relative;
            z-index: 2;
        }

        .video-player-wrapper {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
        }

        .video-player-wrapper:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
        }

        .video-player {
            width: 100%;
            height: 500px;
            border-radius: 15px;
            overflow: hidden;
            background: #000;
            position: relative;
        }

        .video-player iframe,
        .video-player video {
            width: 100%;
            height: 100%;
            border: none;
            border-radius: 15px;
        }

        .video-info-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            margin-top: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .video-title {
            color: #011a2d;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            font-family: 'Montserrat', sans-serif;
        }

        .video-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 25px;
            align-items: center;
        }

        .video-badge {
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .badge-free {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }

        .badge-premium {
            background: linear-gradient(135deg, #ffc107, #e0a800);
            color: #000;
        }

        .badge-login-required {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
        }

        .video-description {
            color: #343a40;
            font-size: 1.1rem;
            line-height: 1.8;
            margin-bottom: 30px;
        }

        .access-denied-card {
            background: linear-gradient(135deg, #a90418, #8b0314);
            color: white;
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            margin-top: 30px;
        }

        .access-denied-card h3 {
            font-size: 2rem;
            margin-bottom: 20px;
        }

        .purchase-button {
            background: linear-gradient(135deg, #a90418, #8b0314);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .purchase-button:hover {
            background: linear-gradient(135deg, #8b0314, #a90418);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(169, 4, 24, 0.3);
            color: white;
            text-decoration: none;
        }

        .login-button {
            background: linear-gradient(135deg, #011a2d, #1a3461);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .login-button:hover {
            background: linear-gradient(135deg, #1a3461, #011a2d);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(1, 26, 45, 0.3);
            color: white;
            text-decoration: none;
        }

        .premium-video-info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-top: 20px;
        }

        .price-info {
            text-align: center;
        }

        .price-display {
            font-size: 2.5rem;
            font-weight: 700;
            color: #a90418;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .access-info {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1.1rem;
            margin-bottom: 0;
        }

        .add-to-cart-button {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-weight: 600;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            width: 100%;
            justify-content: center;
        }

        .add-to-cart-button:hover {
            background: linear-gradient(135deg, #20c997, #28a745);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(40, 167, 69, 0.3);
            color: white;
        }

        .add-to-cart-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .related-videos-section {
            background: #f8f9fa;
            padding: 60px 0;
        }

        .related-video-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            height: 100%;
        }

        .related-video-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .related-video-thumbnail {
            width: 100%;
            height: 200px;
            object-fit: cover;
            background: #f8f9fa;
        }

        .related-video-content {
            padding: 20px;
        }

        .related-video-title {
            color: #011a2d;
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 10px;
            line-height: 1.4;
        }

        .vdocipher-player {
            background: #000;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 500px;
            position: relative;
        }

        .vdocipher-loading {
            color: white;
            text-align: center;
        }

        .video-source-indicator {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        @media (max-width: 768px) {
            .video-hero-section {
                padding-top: 60px;
                min-height: auto;
            }

            .video-player {
                height: 300px;
            }

            .video-title {
                font-size: 2rem;
            }

            .video-meta {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }
    </style>
{% endblock %}

{% block body %}
<!-- Video Hero Section -->
<section class=\"video-hero-section\">
    <div class=\"container\">
        <div class=\"row justify-content-center\">
            <div class=\"col-lg-10\">
                <div class=\"video-container\">
                    <!-- Video Player Wrapper -->
                    <div class=\"video-player-wrapper\">
                        <div class=\"video-player\">
                            {% if has_access %}
                                <!-- Video Source Indicator -->
                                <div class=\"video-source-indicator\">
                                    {% if video.videoSourceType == 'youtube' %}
                                        <i class=\"fab fa-youtube\"></i> YouTube
                                    {% elseif video.videoSourceType == 'vdocipher' %}
                                        <i class=\"fas fa-shield-alt\"></i> VdoCipher
                                    {% else %}
                                        <i class=\"fas fa-play\"></i> Direct
                                    {% endif %}
                                </div>

                                <!-- YouTube Video Player -->
                                {% if video.videoSourceType == 'youtube' and video.youtubeEmbedUrl %}
                                    <iframe src=\"{{ video.youtubeEmbedUrl }}\"
                                            frameborder=\"0\"
                                            allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"
                                            allowfullscreen>
                                    </iframe>

                                <!-- VdoCipher Video Player -->
                                {% elseif video.videoSourceType == 'vdocipher' and video.vdocipherVideoId %}
                                    <div class=\"vdocipher-player\" id=\"vdocipher-player\">
                                        <div class=\"vdocipher-loading\">
                                            <i class=\"fas fa-spinner fa-spin\" style=\"font-size: 3rem; margin-bottom: 1rem;\"></i>
                                            <h4>Loading Secure Video...</h4>
                                            <p>Initializing DRM-protected playback</p>
                                        </div>
                                    </div>

                                <!-- Direct Upload Video Player -->
                                {% elseif video.videoFile %}
                                    <video controls preload=\"metadata\" poster=\"{{ video.thumbnail ? asset('uploads/videos/thumbnails/' ~ video.thumbnail) : '' }}\">
                                        <source src=\"{{ asset('uploads/videos/files/' ~ video.videoFile) }}\" type=\"video/mp4\">
                                        <p>Your browser doesn't support HTML5 video. <a href=\"{{ asset('uploads/videos/files/' ~ video.videoFile) }}\">Download the video</a> instead.</p>
                                    </video>

                                <!-- No Video Source -->
                                {% else %}
                                    <div class=\"d-flex align-items-center justify-content-center h-100\">
                                        <div class=\"text-center text-white\">
                                            <i class=\"fas fa-exclamation-triangle\" style=\"font-size: 3rem; margin-bottom: 1rem;\"></i>
                                            <h4>Video Not Available</h4>
                                            <p>This video is currently being processed. Please try again later.</p>
                                        </div>
                                    </div>
                                {% endif %}
                            {% else %}
                                <!-- Access Denied - Show Thumbnail with Overlay -->
                                <div class=\"d-flex align-items-center justify-content-center h-100\" style=\"background: {{ video.thumbnail ? 'url(' ~ asset('uploads/videos/thumbnails/' ~ video.thumbnail) ~ ')' : '#000' }} center/cover;\">
                                    <div style=\"background: rgba(0, 0, 0, 0.8); width: 100%; height: 100%; display: flex; align-items: center; justify-content: center;\">
                                        <div class=\"text-center text-white\">
                                            <i class=\"fas fa-lock\" style=\"font-size: 4rem; margin-bottom: 1rem;\"></i>
                                            <h3>Premium Content</h3>
                                            <p class=\"mb-0\">This video requires access to view</p>
                                        </div>
                                    </div>
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Video Information Card -->
                    <div class=\"video-info-card\">
                        <h1 class=\"video-title\">{{ video.title }}</h1>

                        <!-- Video Meta Information -->
                        <div class=\"video-meta\">
                            <!-- Access Level Badge -->
                            {% if video.accessLevel == 'public_free' %}
                                <span class=\"video-badge badge-free\">
                                    <i class=\"fas fa-globe\"></i>
                                    Public Free
                                </span>
                            {% elseif video.accessLevel == 'login_required_free' %}
                                <span class=\"video-badge badge-login-required\">
                                    <i class=\"fas fa-user\"></i>
                                    Login Required
                                </span>
                            {% else %}
                                <span class=\"video-badge badge-premium\">
                                    <i class=\"fas fa-crown\"></i>
                                    Premium - {{ video.formattedPrice }}
                                    {% if video.accessDuration %}
                                        ({{ video.formattedAccessDuration }})
                                    {% endif %}
                                </span>
                            {% endif %}

                            <!-- Category Badge -->
                            {% if video.category %}
                                <span class=\"video-badge\" style=\"background: #6c757d; color: white;\">
                                    <i class=\"fas fa-tag\"></i>
                                    {{ video.category }}
                                </span>
                            {% endif %}

                            <!-- Video Source Badge -->
                            <span class=\"video-badge\" style=\"background: #17a2b8; color: white;\">
                                {% if video.videoSourceType == 'youtube' %}
                                    <i class=\"fab fa-youtube\"></i>
                                    YouTube
                                {% elseif video.videoSourceType == 'vdocipher' %}
                                    <i class=\"fas fa-shield-alt\"></i>
                                    VdoCipher
                                {% else %}
                                    <i class=\"fas fa-upload\"></i>
                                    Direct Upload
                                {% endif %}
                            </span>

                            <!-- Upload Date -->
                            <span class=\"video-badge\" style=\"background: #f8f9fa; color: #6c757d; border: 1px solid #dee2e6;\">
                                <i class=\"fas fa-calendar\"></i>
                                {{ video.createdAt|date('M d, Y') }}
                            </span>
                        </div>

                        <!-- Video Description -->
                        {% if video.description %}
                            <div class=\"video-description\">
                                {{ video.description|nl2br }}
                            </div>
                        {% endif %}

                        <!-- Access Control Actions -->
                        {% if not has_access %}
                            {% if video.accessLevel == 'login_required_free' and not app.user %}
                                <!-- Login Required for Free Video -->
                                <div class=\"text-center\">
                                    <p class=\"mb-3\">Please log in to watch this free video.</p>
                                    <a href=\"{{ path('app_login') }}\" class=\"login-button\">
                                        <i class=\"fas fa-sign-in-alt\"></i>
                                        Login to Watch
                                    </a>
                                </div>
                            {% elseif video.accessLevel == 'premium' %}
                                <!-- Premium Video Purchase -->
                                <div class=\"text-center\">
                                    {% if not app.user %}
                                        <p class=\"mb-3\">Please log in to purchase this premium video.</p>
                                        <a href=\"{{ path('app_login') }}\" class=\"login-button me-3\">
                                            <i class=\"fas fa-sign-in-alt\"></i>
                                            Login
                                        </a>
                                    {% else %}
                                        <div class=\"premium-video-info\">
                                            <div class=\"price-info mb-3\">
                                                <h4 class=\"price-display\">{{ video.formattedPrice }}</h4>
                                                <p class=\"access-info\">
                                                    {% if video.accessDuration %}
                                                        {{ video.accessDuration }} days access
                                                    {% else %}
                                                        Lifetime access
                                                    {% endif %}
                                                </p>
                                            </div>
                                            <button class=\"add-to-cart-button\" onclick=\"addToCart('video', {{ video.id }})\">
                                                <i class=\"fas fa-cart-plus\"></i>
                                                Add to Cart
                                            </button>
                                        </div>
                                    {% endif %}
                                </div>
                            {% endif %}
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Videos Section -->
{% if related_videos|length > 0 %}
<section class=\"related-videos-section\">
    <div class=\"container\">
        <div class=\"row\">
            <div class=\"col-12\">
                <h2 class=\"text-center mb-5\" style=\"color: #011a2d; font-family: 'Montserrat', sans-serif; font-weight: 700;\">
                    Related Videos
                </h2>
            </div>
        </div>
        <div class=\"row g-4\">
            {% for related_video in related_videos %}
                <div class=\"col-lg-3 col-md-6\">
                    <div class=\"related-video-card\">
                        <a href=\"{{ path('app_video_show', {'id': related_video.id}) }}\" class=\"text-decoration-none\">
                            {% if related_video.thumbnail %}
                                <img src=\"{{ asset('uploads/videos/thumbnails/' ~ related_video.thumbnail) }}\"
                                     alt=\"{{ related_video.title }}\"
                                     class=\"related-video-thumbnail\">
                            {% else %}
                                <div class=\"related-video-thumbnail d-flex align-items-center justify-content-center\" style=\"background: #f8f9fa;\">
                                    <i class=\"fas fa-video text-muted\" style=\"font-size: 3rem;\"></i>
                                </div>
                            {% endif %}
                            <div class=\"related-video-content\">
                                <h5 class=\"related-video-title\">{{ related_video.title }}</h5>
                                <div class=\"d-flex justify-content-between align-items-center\">
                                    <small class=\"text-muted\">
                                        <i class=\"fas fa-calendar me-1\"></i>
                                        {{ related_video.createdAt|date('M d, Y') }}
                                    </small>
                                    {% if related_video.accessLevel == 'public_free' %}
                                        <span class=\"badge bg-success\">Free</span>
                                    {% elseif related_video.accessLevel == 'login_required_free' %}
                                        <span class=\"badge bg-info\">Login Required</span>
                                    {% else %}
                                        <span class=\"badge bg-warning text-dark\">{{ related_video.formattedPrice }}</span>
                                    {% endif %}
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>
</section>
{% endif %}
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        // VdoCipher Player Initialization
        {% if has_access and video.videoSourceType == 'vdocipher' and video.vdocipherVideoId %}
        document.addEventListener('DOMContentLoaded', function() {
            initializeVdoCipherPlayer();
        });

        function initializeVdoCipherPlayer() {
            // Get video access token from server
            fetch('{{ path('app_video_vdocipher_token', {'id': video.id}) }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.otp && data.playbackInfo) {
                    // Initialize VdoCipher player
                    new VdoPlayer({
                        otp: data.otp,
                        playbackInfo: data.playbackInfo,
                        theme: \"9bb535\",
                        container: document.querySelector(\"#vdocipher-player\"),
                        width: \"100%\",
                        height: \"100%\"
                    });
                } else {
                    showVdoCipherError('Failed to load video. Please try again.');
                }
            })
            .catch(error => {
                console.error('VdoCipher initialization error:', error);
                showVdoCipherError('Unable to load video player. Please refresh the page.');
            });
        }

        function showVdoCipherError(message) {
            const player = document.getElementById('vdocipher-player');
            player.innerHTML = `
                <div class=\"vdocipher-loading\">
                    <i class=\"fas fa-exclamation-triangle text-warning\" style=\"font-size: 3rem; margin-bottom: 1rem;\"></i>
                    <h4>Video Loading Error</h4>
                    <p>\${message}</p>
                    <button class=\"btn btn-primary mt-3\" onclick=\"initializeVdoCipherPlayer()\">
                        <i class=\"fas fa-redo\"></i> Retry
                    </button>
                </div>
            `;
        }
        {% endif %}

        // Video Purchase Function
        function purchaseVideo(videoId) {
            // Show loading state
            const button = event.target.closest('.purchase-button');
            const originalContent = button.innerHTML;
            button.innerHTML = '<i class=\"fas fa-spinner fa-spin\"></i> Processing...';
            button.disabled = true;

            // Create Stripe checkout session
            fetch('{{ path('app_video_purchase') }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    video_id: videoId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.checkout_url) {
                    // Redirect to Stripe checkout
                    window.location.href = data.checkout_url;
                } else {
                    // Show error message
                    alert(data.message || 'Unable to process purchase. Please try again.');
                    button.innerHTML = originalContent;
                    button.disabled = false;
                }
            })
            .catch(error => {
                console.error('Purchase error:', error);
                alert('Unable to process purchase. Please try again.');
                button.innerHTML = originalContent;
                button.disabled = false;
            });
        }

        // Video Analytics Tracking
        {% if has_access %}
        document.addEventListener('DOMContentLoaded', function() {
            // Track video view
            fetch('{{ path('app_video_track_view', {'slug': video.slug}) }}', {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            // Track video engagement
            let watchTime = 0;
            const trackingInterval = setInterval(() => {
                watchTime += 5; // Track every 5 seconds

                // Send tracking data every 30 seconds
                if (watchTime % 30 === 0) {
                    fetch('{{ path('app_video_track_engagement', {'slug': video.slug}) }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: JSON.stringify({
                            watch_time: watchTime
                        })
                    });
                }
            }, 5000);

            // Stop tracking when user leaves page
            window.addEventListener('beforeunload', () => {
                clearInterval(trackingInterval);
            });
        });
        {% endif %}

        // Smooth scroll for related videos
        document.querySelectorAll('a[href^=\"#\"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add to Cart Function
        function addToCart(type, id) {
            const button = event.target.closest('.add-to-cart-button');
            const originalContent = button.innerHTML;

            // Show loading state
            button.innerHTML = '<i class=\"fas fa-spinner fa-spin\"></i> Adding...';
            button.disabled = true;

            fetch('{{ path('app_cart_add') }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: `type=\${type}&id=\${id}&quantity=1`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    button.innerHTML = '<i class=\"fas fa-check\"></i> Added to Cart!';
                    button.style.background = 'linear-gradient(135deg, #28a745, #20c997)';

                    // Update cart widget if it exists
                    if (typeof updateCartWidget === 'function') {
                        updateCartWidget();
                    }

                    // Reset button after 2 seconds
                    setTimeout(() => {
                        button.innerHTML = originalContent;
                        button.disabled = false;
                        button.style.background = '';
                    }, 2000);
                } else {
                    // Show error message
                    alert(data.message || 'Unable to add item to cart. Please try again.');
                    button.innerHTML = originalContent;
                    button.disabled = false;
                }
            })
            .catch(error => {
                console.error('Add to cart error:', error);
                alert('Unable to add item to cart. Please try again.');
                button.innerHTML = originalContent;
                button.disabled = false;
            });
        }
    </script>

    <!-- VdoCipher Player Script -->
    {% if has_access and video.videoSourceType == 'vdocipher' %}
    <script src=\"https://player.vdocipher.com/v2/api.js\"></script>
    {% endif %}
{% endblock %}", "video/show.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\video\\show.html.twig");
    }
}
