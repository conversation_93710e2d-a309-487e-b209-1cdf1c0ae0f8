{% extends 'admin/base.html.twig' %}

{% block title %}Video Details - Capitol Academy Admin{% endblock %}

{% block page_title %}Video Details{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{{ path('admin_dashboard') }}">Home</a></li>
<li class="breadcrumb-item"><a href="{{ path('admin_video_index') }}">Videos</a></li>
<li class="breadcrumb-item active">{{ video.title }}</li>
{% endblock %}

{% block content %}
<div class="container-fluid">


    <!-- Integrated Header with Content -->
    <div class="card border-0 shadow-lg mb-4">
        <div class="card-header" style="background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h2 class="card-title mb-0" style="font-size: 1.8rem; font-weight: 600;">
                        <i class="fas fa-video mr-3" style="font-size: 2rem;"></i>
                        Video Details: {{ video.title }}
                    </h2>
                </div>
                <div class="col-md-6">
                    <div class="d-flex justify-content-end align-items-center flex-wrap">
                        <!-- Edit Video Button (Icon Only) -->
                        <a href="{{ path('admin_video_edit', {'slug': video.slug}) }}"
                           class="btn me-2 mb-2 mb-md-0"
                           style="border-radius: 50%; width: 45px; height: 45px; display: inline-flex; align-items: center; justify-content: center; background: white; border: 2px solid #011a2d; transition: all 0.3s ease;"
                           onmouseover="this.style.background='#011a2d'; this.querySelector('i').style.color='white';"
                           onmouseout="this.style.background='white'; this.querySelector('i').style.color='#011a2d';"
                           title="Edit Video">
                            <i class="fas fa-edit" style="color: #011a2d;"></i>
                        </a>

                        <!-- Print Video Button (Icon Only) -->
                        <a href="javascript:void(0)" onclick="window.print()"
                           class="btn me-2 mb-2 mb-md-0"
                           style="border-radius: 50%; width: 45px; height: 45px; display: inline-flex; align-items: center; justify-content: center; background: white; border: 2px solid #011a2d; transition: all 0.3s ease;"
                           onmouseover="this.style.background='#011a2d'; this.querySelector('i').style.color='white';"
                           onmouseout="this.style.background='white'; this.querySelector('i').style.color='#011a2d';"
                           title="Print Video Details">
                            <i class="fas fa-print" style="color: #011a2d;"></i>
                        </a>

                        <!-- Back to Videos Button -->
                        <a href="{{ path('admin_video_index') }}"
                           class="btn mb-2 mb-md-0"
                           style="font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;"
                           onmouseover="this.style.background='#011a2d'; this.style.color='white';"
                           onmouseout="this.style.background='white'; this.style.color='#011a2d';">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Videos
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="card-body">
            <div class="row">
                <div class="col-12">
                    <!-- Video Title and Category (same line) -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-video text-primary mr-1"></i>
                                    Video Title
                                </label>
                                <div class="enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;">
                                    {{ video.title }}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-tag text-primary mr-1"></i>
                                    Category
                                </label>
                                <div class="enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;">
                                    {{ video.category ?? 'Uncategorized' }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Description -->
                    <div class="form-group mb-4">
                        <label class="form-label">
                            <i class="fas fa-align-left text-primary mr-1"></i>
                            Description
                        </label>
                        <div class="enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; min-height: 80px;">
                            {{ video.description ?? 'No description provided' }}
                        </div>
                    </div>

                    <!-- Video Source Type and Access Level (same line) -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-source text-primary mr-1"></i>
                                    Video Source Type
                                </label>
                                <div class="enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;">
                                    {% if video.videoSourceType == 'youtube' %}
                                        <i class="fab fa-youtube text-danger mr-2"></i>YouTube
                                    {% elseif video.videoSourceType == 'vdocipher' %}
                                        <i class="fas fa-shield-alt text-success mr-2"></i>VdoCipher
                                    {% else %}
                                        <i class="fas fa-upload text-info mr-2"></i>Direct Upload
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-lock text-primary mr-1"></i>
                                    Access Level
                                </label>
                                <div class="enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;">
                                    {% if video.accessLevel == 'public_free' %}
                                        <i class="fas fa-globe text-success mr-2"></i>Public Free
                                    {% elseif video.accessLevel == 'login_required_free' %}
                                        <i class="fas fa-user text-warning mr-2"></i>Login Required Free
                                    {% else %}
                                        <i class="fas fa-crown text-danger mr-2"></i>Premium
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Premium Pricing (if premium) -->
                    {% if video.accessLevel == 'premium' %}
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-dollar-sign text-primary mr-1"></i>
                                    Price
                                </label>
                                <div class="enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; font-weight: 600;">
                                    ${{ video.price ?? '0.00' }}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-clock text-primary mr-1"></i>
                                    Access Duration in Days
                                </label>
                                <div class="enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;">
                                    {{ video.accessDuration ?? 'Unlimited' }} {% if video.accessDuration %}days{% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- YouTube URL (if YouTube) -->
                    {% if video.videoSourceType == 'youtube' and video.youtubeUrl %}
                    <div class="form-group mb-4">
                        <label class="form-label">
                            <i class="fab fa-youtube text-danger mr-1"></i>
                            YouTube URL
                        </label>
                        <div class="enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;">
                            <a href="{{ video.youtubeUrl }}" target="_blank" class="text-decoration-none">{{ video.youtubeUrl }}</a>
                        </div>
                    </div>
                    {% endif %}

                    <!-- VdoCipher ID (if VdoCipher) -->
                    {% if video.videoSourceType == 'vdocipher' and video.vdocipherVideoId %}
                    <div class="form-group mb-4">
                        <label class="form-label">
                            <i class="fas fa-shield-alt text-success mr-1"></i>
                            VdoCipher ID
                        </label>
                        <div class="enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d; font-family: monospace;">
                            {{ video.vdocipherVideoId }}
                        </div>
                    </div>
                    {% endif %}
                    <!-- Thumbnail and Video (same line, equal height and width) -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-image text-primary mr-1"></i>
                                    Thumbnail
                                </label>
                                <div class="d-flex justify-content-center">
                                    {% if video.thumbnail %}
                                        <img src="{{ asset('uploads/videos/thumbnails/' ~ video.thumbnail) }}"
                                             alt="Video Thumbnail"
                                             class="img-fluid"
                                             style="width: 400px; height: 225px; object-fit: cover; border: 2px solid #1e3c72; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);">
                                    {% else %}
                                        <div class="d-flex align-items-center justify-content-center"
                                             style="width: 400px; height: 225px; border: 2px solid #ced4da; border-radius: 8px; background: #f8f9fa;">
                                            <div class="text-center text-muted">
                                                <i class="fas fa-image" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                                                <p>No thumbnail available</p>
                                            </div>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-play text-primary mr-1"></i>
                                    Video Player
                                </label>
                                <div class="d-flex justify-content-center">
                                    {% if video.videoSourceType == 'youtube' and video.youtubeEmbedUrl %}
                                        <iframe src="{{ video.youtubeEmbedUrl }}"
                                                style="width: 400px; height: 225px; border: 2px solid #1e3c72; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);"
                                                frameborder="0"
                                                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                                allowfullscreen>
                                        </iframe>
                                    {% elseif video.videoSourceType == 'vdocipher' and video.vdocipherVideoId %}
                                        <div class="d-flex align-items-center justify-content-center"
                                             style="width: 400px; height: 225px; border: 2px solid #1e3c72; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); background: #f8f9fa;">
                                            <div class="text-center">
                                                <i class="fas fa-shield-alt text-success" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                                                <h5>VdoCipher Protected Video</h5>
                                                <p class="text-muted">ID: {{ video.vdocipherVideoId }}</p>
                                            </div>
                                        </div>
                                    {% elseif video.videoFile %}
                                        <video controls
                                               style="width: 400px; height: 225px; border: 2px solid #1e3c72; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);">
                                            <source src="{{ asset('uploads/videos/files/' ~ video.videoFile) }}" type="video/mp4">
                                            Your browser does not support the video tag.
                                        </video>
                                    {% else %}
                                        <div class="d-flex align-items-center justify-content-center"
                                             style="width: 400px; height: 225px; border: 2px solid #ced4da; border-radius: 8px; background: #f8f9fa;">
                                            <div class="text-center text-muted">
                                                <i class="fas fa-video" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                                                <p>No video available</p>
                                            </div>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Status and Creation Date -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-toggle-on text-primary mr-1"></i>
                                    Status
                                </label>
                                <div class="enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;">
                                    {% if video.isActive %}
                                        <span class="badge bg-success" style="font-size: 0.9rem;">
                                            <i class="fas fa-check-circle mr-1"></i>Active
                                        </span>
                                    {% else %}
                                        <span class="badge bg-secondary" style="font-size: 0.9rem;">
                                            <i class="fas fa-pause-circle mr-1"></i>Inactive
                                        </span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-calendar-plus text-primary mr-1"></i>
                                    Creation Date
                                </label>
                                <div class="enhanced-display-field" style="background: #f8f9fa; border: 2px solid #ced4da; border-radius: 0.375rem; padding: 0.75rem 1rem; color: #011a2d;">
                                    {{ video.createdAt|date('F j, Y \\a\\t g:i A') }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}
