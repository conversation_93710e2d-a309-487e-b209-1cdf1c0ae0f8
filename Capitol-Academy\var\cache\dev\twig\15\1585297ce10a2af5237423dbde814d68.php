<?php

use Twig\Environment;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Extension\CoreExtension;
use Twig\Extension\SandboxExtension;
use Twig\Markup;
use Twig\Sandbox\SecurityError;
use Twig\Sandbox\SecurityNotAllowedTagError;
use Twig\Sandbox\SecurityNotAllowedFilterError;
use Twig\Sandbox\SecurityNotAllowedFunctionError;
use Twig\Source;
use Twig\Template;
use Twig\TemplateWrapper;

/* video/list.html.twig */
class __TwigTemplate_00039183e1dfe2506f7d88d492f2d341 extends Template
{
    private Source $source;
    /**
     * @var array<string, Template>
     */
    private array $macros = [];

    public function __construct(Environment $env)
    {
        parent::__construct($env);

        $this->source = $this->getSourceContext();

        $this->blocks = [
            'title' => [$this, 'block_title'],
            'meta_description' => [$this, 'block_meta_description'],
            'stylesheets' => [$this, 'block_stylesheets'],
            'body' => [$this, 'block_body'],
            'javascripts' => [$this, 'block_javascripts'],
        ];
    }

    protected function doGetParent(array $context): bool|string|Template|TemplateWrapper
    {
        // line 1
        return "base.html.twig";
    }

    protected function doDisplay(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "video/list.html.twig"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "template", "video/list.html.twig"));

        $this->parent = $this->load("base.html.twig", 1);
        yield from $this->parent->unwrap()->yield($context, array_merge($this->blocks, $blocks));
        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

    }

    // line 3
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_title(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "title"));

        yield "Video Library - Capitol Academy";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 5
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_meta_description(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "meta_description"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "meta_description"));

        yield "Explore our comprehensive video library featuring professional trading education content, market analysis, and expert insights from Capitol Academy.";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 7
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_stylesheets(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "stylesheets"));

        // line 8
        yield "    ";
        yield from $this->yieldParentBlock("stylesheets", $context, $blocks);
        yield "
    <style>
        /* Video Library Styles */
        .video-library-hero {
            background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%);
            padding: 120px 0 80px;
            position: relative;
        }

        .hero-content {
            text-align: center;
            color: white;
        }

        .hero-title {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 15px;
            font-family: 'Montserrat', sans-serif;
        }

        .hero-subtitle {
            font-size: 1.2rem;
            margin-bottom: 0;
            opacity: 0.9;
            font-family: 'Calibri', Arial, sans-serif;
        }

        .search-section {
            background: white;
            padding: 40px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .search-container {
            max-width: 600px;
            margin: 0 auto;
            position: relative;
        }

        .search-box {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 50px;
            padding: 15px 50px 15px 25px;
            font-size: 1rem;
            width: 100%;
            transition: all 0.3s ease;
        }

        .search-box:focus {
            outline: none;
            border-color: #011a2d;
            background: white;
            box-shadow: 0 0 0 3px rgba(1, 26, 45, 0.1);
        }

        .search-icon {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
            font-size: 1.1rem;
        }

        .filter-section {
            background: #f8f9fa;
            padding: 30px 0;
        }

        .filter-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            justify-content: center;
            margin-bottom: 20px;
        }

        .filter-btn {
            background: white;
            color: #6c757d;
            border: 2px solid #e9ecef;
            padding: 8px 20px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
            font-size: 0.9rem;
        }

        .filter-btn:hover,
        .filter-btn.active {
            background: #011a2d;
            border-color: #011a2d;
            color: white;
            transform: translateY(-1px);
        }

        .video-catalog-section {
            padding: 60px 0;
            background: white;
        }

        .section-title {
            text-align: center;
            margin-bottom: 40px;
        }

        .section-title h2 {
            font-family: 'Montserrat', sans-serif;
            font-size: 2.5rem;
            font-weight: 700;
            color: #011a2d;
            margin-bottom: 10px;
        }

        .section-title p {
            font-size: 1.1rem;
            color: #6c757d;
            margin: 0;
        }

        .video-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 25px;
            margin-top: 40px;
        }

        .video-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(1, 26, 45, 0.1);
            transition: all 0.3s ease;
            position: relative;
            height: 100%;
            display: flex;
            flex-direction: column;
            border: 1px solid #f1f3f4;
        }

        .video-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(1, 26, 45, 0.15);
            border-color: #e9ecef;
        }

        .video-thumbnail {
            position: relative;
            height: 180px;
            overflow: hidden;
            background: #f8f9fa;
        }

        .video-thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .video-card:hover .video-thumbnail img {
            transform: scale(1.03);
        }

        .video-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(1, 26, 45, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .video-card:hover .video-overlay {
            opacity: 1;
        }

        .play-button {
            width: 50px;
            height: 50px;
            background: #a90418;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            transition: all 0.3s ease;
        }

        .play-button:hover {
            background: #8b0314;
            transform: scale(1.1);
        }

        .video-badges {
            position: absolute;
            top: 12px;
            left: 12px;
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .video-badge {
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
            backdrop-filter: blur(10px);
        }

        .badge-free {
            background: rgba(40, 167, 69, 0.95);
            color: white;
        }

        .badge-premium {
            background: rgba(169, 4, 24, 0.95);
            color: white;
        }

        .badge-login-required {
            background: rgba(1, 26, 45, 0.95);
            color: white;
        }

        .video-content {
            padding: 20px;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }

        .video-title {
            color: #011a2d;
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 12px;
            line-height: 1.4;
            font-family: 'Montserrat', sans-serif;
            font-family: 'Montserrat', sans-serif;
        }

        .video-description {
            color: #6c757d;
            font-size: 0.95rem;
            line-height: 1.6;
            margin-bottom: 20px;
            flex-grow: 1;
        }

        .video-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            font-size: 0.9rem;
            color: #6c757d;
        }

        .video-category {
            background: #e9ecef;
            color: #495057;
            padding: 4px 12px;
            border-radius: 12px;
            font-weight: 600;
        }

        .video-date {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .video-actions {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .btn-watch {
            background: linear-gradient(135deg, #a90418, #8b0314);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            flex-grow: 1;
            text-align: center;
        }

        .btn-watch:hover {
            background: linear-gradient(135deg, #8b0314, #a90418);
            transform: translateY(-2px);
            color: white;
            text-decoration: none;
        }

        .btn-purchase {
            background: linear-gradient(135deg, #ffc107, #e0a800);
            color: #000;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            flex-grow: 1;
            text-align: center;
        }

        .btn-purchase:hover {
            background: linear-gradient(135deg, #e0a800, #ffc107);
            transform: translateY(-2px);
            color: #000;
            text-decoration: none;
        }

        .btn-login {
            background: linear-gradient(135deg, #011a2d, #1a3461);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            flex-grow: 1;
            text-align: center;
        }

        .btn-login:hover {
            background: linear-gradient(135deg, #1a3461, #011a2d);
            transform: translateY(-2px);
            color: white;
            text-decoration: none;
        }

        .btn-details {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.3s ease;
            flex-grow: 1;
            text-align: center;
            text-decoration: none;
        }

        .btn-details:hover {
            background: linear-gradient(135deg, #138496, #17a2b8);
            transform: translateY(-2px);
            color: white;
            text-decoration: none;
        }

        .no-videos {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .no-videos i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .loading-spinner {
            text-align: center;
            padding: 40px;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #a90418;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }

            .video-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .filter-buttons {
                justify-content: flex-start;
            }

            .search-filter-section {
                padding: 20px;
            }
        }
    </style>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 431
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_body(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "body"));

        // line 432
        yield "<!-- Video Library Hero Section -->
<section class=\"video-library-hero\">
    <div class=\"container\">
        <div class=\"hero-content\">
            <h1 class=\"hero-title\">Video Library</h1>
            <p class=\"hero-subtitle\">
                Explore our comprehensive collection of professional trading education videos,
                market analysis, and expert insights to enhance your trading skills.
            </p>
        </div>
    </div>
</section>

<!-- Search Section -->
<section class=\"search-section\">
    <div class=\"container\">
        <div class=\"search-container\">
            <input type=\"text\"
                   class=\"search-box\"
                   id=\"video-search\"
                   placeholder=\"Search videos by title, description, or category...\">
            <i class=\"fas fa-search search-icon\"></i>
        </div>
    </div>
</section>

<!-- Filter Section -->
<section class=\"filter-section\">
    <div class=\"container\">
        <div class=\"filter-buttons\">
            <button class=\"filter-btn active\" data-filter=\"all\">All Videos</button>
            <button class=\"filter-btn\" data-filter=\"free\">Free</button>
            <button class=\"filter-btn\" data-filter=\"premium\">Premium</button>
            <button class=\"filter-btn\" data-filter=\"youtube\">YouTube</button>
            <button class=\"filter-btn\" data-filter=\"vdocipher\">VdoCipher</button>
        </div>
    </div>
</section>
<!-- Video Catalog Section -->
<section class=\"video-catalog-section\">
    <div class=\"container\">
        <div class=\"section-title\">
            <h2>Our Video Collection</h2>
            <p>Professional trading education content from industry experts</p>
        </div>
                <!-- Loading Spinner -->
                <div class=\"loading-spinner\" id=\"loading-spinner\" style=\"display: none;\">
                    <div class=\"spinner\"></div>
                    <p>Loading videos...</p>
                </div>

                <!-- Video Grid -->
                <div class=\"video-grid\" id=\"video-grid\">
                    ";
        // line 485
        if ((Twig\Extension\CoreExtension::length($this->env->getCharset(), (isset($context["videos"]) || array_key_exists("videos", $context) ? $context["videos"] : (function () { throw new RuntimeError('Variable "videos" does not exist.', 485, $this->source); })())) > 0)) {
            // line 486
            yield "                        ";
            $context['_parent'] = $context;
            $context['_seq'] = CoreExtension::ensureTraversable((isset($context["videos"]) || array_key_exists("videos", $context) ? $context["videos"] : (function () { throw new RuntimeError('Variable "videos" does not exist.', 486, $this->source); })()));
            foreach ($context['_seq'] as $context["_key"] => $context["video"]) {
                // line 487
                yield "                            <div class=\"video-card\"
                                 data-category=\"";
                // line 488
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["video"], "category", [], "any", false, false, false, 488), "html", null, true);
                yield "\"
                                 data-access-level=\"";
                // line 489
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["video"], "accessLevel", [], "any", false, false, false, 489), "html", null, true);
                yield "\"
                                 data-title=\"";
                // line 490
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::lower($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["video"], "title", [], "any", false, false, false, 490)), "html", null, true);
                yield "\"
                                 data-description=\"";
                // line 491
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(Twig\Extension\CoreExtension::lower($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["video"], "description", [], "any", false, false, false, 491)), "html", null, true);
                yield "\">

                                <!-- Video Thumbnail -->
                                <div class=\"video-thumbnail\">
                                    ";
                // line 495
                if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["video"], "thumbnail", [], "any", false, false, false, 495)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    // line 496
                    yield "                                        <img src=\"";
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\AssetExtension']->getAssetUrl(("uploads/videos/thumbnails/" . CoreExtension::getAttribute($this->env, $this->source, $context["video"], "thumbnail", [], "any", false, false, false, 496))), "html", null, true);
                    yield "\"
                                             alt=\"";
                    // line 497
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["video"], "title", [], "any", false, false, false, 497), "html", null, true);
                    yield "\"
                                             loading=\"lazy\">
                                    ";
                } else {
                    // line 500
                    yield "                                        <div style=\"width: 100%; height: 100%; background: linear-gradient(135deg, #f8f9fa, #e9ecef); display: flex; align-items: center; justify-content: center;\">
                                            <i class=\"fas fa-video text-muted\" style=\"font-size: 3rem;\"></i>
                                        </div>
                                    ";
                }
                // line 504
                yield "
                                    <!-- Video Overlay -->
                                    <div class=\"video-overlay\">
                                        <div class=\"play-button\">
                                            <i class=\"fas fa-play\"></i>
                                        </div>
                                    </div>

                                    <!-- Video Badges -->
                                    <div class=\"video-badges\">
                                        <!-- Access Level Badge -->
                                        ";
                // line 515
                if ((CoreExtension::getAttribute($this->env, $this->source, $context["video"], "accessLevel", [], "any", false, false, false, 515) == "public_free")) {
                    // line 516
                    yield "                                            <span class=\"video-badge badge-free\">
                                                <i class=\"fas fa-globe\"></i> Free
                                            </span>
                                        ";
                } elseif ((CoreExtension::getAttribute($this->env, $this->source,                 // line 519
$context["video"], "accessLevel", [], "any", false, false, false, 519) == "login_required_free")) {
                    // line 520
                    yield "                                            <span class=\"video-badge badge-login-required\">
                                                <i class=\"fas fa-user\"></i> Login Required
                                            </span>
                                        ";
                } else {
                    // line 524
                    yield "                                            <span class=\"video-badge badge-premium\">
                                                <i class=\"fas fa-crown\"></i> ";
                    // line 525
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["video"], "formattedPrice", [], "any", false, false, false, 525), "html", null, true);
                    yield "
                                            </span>
                                        ";
                }
                // line 528
                yield "
                                        <!-- Video Source Badge -->
                                        <span class=\"video-badge badge-source\">
                                            ";
                // line 531
                if ((CoreExtension::getAttribute($this->env, $this->source, $context["video"], "videoSourceType", [], "any", false, false, false, 531) == "youtube")) {
                    // line 532
                    yield "                                                <i class=\"fab fa-youtube\"></i> YouTube
                                            ";
                } elseif ((CoreExtension::getAttribute($this->env, $this->source,                 // line 533
$context["video"], "videoSourceType", [], "any", false, false, false, 533) == "vdocipher")) {
                    // line 534
                    yield "                                                <i class=\"fas fa-shield-alt\"></i> VdoCipher
                                            ";
                } else {
                    // line 536
                    yield "                                                <i class=\"fas fa-play\"></i> Direct
                                            ";
                }
                // line 538
                yield "                                        </span>
                                    </div>
                                </div>

                                <!-- Video Content -->
                                <div class=\"video-content\">
                                    <h3 class=\"video-title\">";
                // line 544
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["video"], "title", [], "any", false, false, false, 544), "html", null, true);
                yield "</h3>

                                    <!-- Video Meta -->
                                    <div class=\"video-meta\">
                                        ";
                // line 548
                if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["video"], "category", [], "any", false, false, false, 548)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    // line 549
                    yield "                                            <span class=\"video-category\">";
                    yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["video"], "category", [], "any", false, false, false, 549), "html", null, true);
                    yield "</span>
                                        ";
                }
                // line 551
                yield "                                        <span class=\"video-date\">
                                            <i class=\"fas fa-calendar\"></i>
                                            ";
                // line 553
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Twig\Extension\CoreExtension']->formatDate(CoreExtension::getAttribute($this->env, $this->source, $context["video"], "createdAt", [], "any", false, false, false, 553), "M d, Y"), "html", null, true);
                yield "
                                        </span>
                                    </div>

                                    <!-- Video Description -->
                                    ";
                // line 558
                if ((($tmp = CoreExtension::getAttribute($this->env, $this->source, $context["video"], "description", [], "any", false, false, false, 558)) && $tmp instanceof Markup ? (string) $tmp : $tmp)) {
                    // line 559
                    yield "                                        <p class=\"video-description\">
                                            ";
                    // line 560
                    yield (((Twig\Extension\CoreExtension::length($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["video"], "description", [], "any", false, false, false, 560)) > 120)) ? ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape((Twig\Extension\CoreExtension::slice($this->env->getCharset(), CoreExtension::getAttribute($this->env, $this->source, $context["video"], "description", [], "any", false, false, false, 560), 0, 120) . "..."), "html", null, true)) : ($this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape(CoreExtension::getAttribute($this->env, $this->source, $context["video"], "description", [], "any", false, false, false, 560), "html", null, true)));
                    yield "
                                        </p>
                                    ";
                }
                // line 563
                yield "
                                    <!-- Video Actions -->
                                    <div class=\"video-actions\">
                                        <a href=\"";
                // line 566
                yield $this->env->getRuntime('Twig\Runtime\EscaperRuntime')->escape($this->extensions['Symfony\Bridge\Twig\Extension\RoutingExtension']->getPath("app_video_show", ["slug" => CoreExtension::getAttribute($this->env, $this->source, $context["video"], "slug", [], "any", false, false, false, 566)]), "html", null, true);
                yield "\" class=\"btn-details\">
                                            <i class=\"fas fa-info-circle\"></i> See Details
                                        </a>
                                    </div>
                                </div>
                            </div>
                        ";
            }
            $_parent = $context['_parent'];
            unset($context['_seq'], $context['_key'], $context['video'], $context['_parent']);
            $context = array_intersect_key($context, $_parent) + $_parent;
            // line 573
            yield "                    ";
        } else {
            // line 574
            yield "                        <div class=\"no-videos\">
                            <i class=\"fas fa-video\"></i>
                            <h3>No Videos Available</h3>
                            <p>We're working on adding more content. Please check back soon!</p>
                        </div>
                    ";
        }
        // line 580
        yield "                </div>

                <!-- No Results Message -->
                <div class=\"no-videos\" id=\"no-results\" style=\"display: none;\">
                    <i class=\"fas fa-search\"></i>
                    <h3>No Videos Found</h3>
                    <p>Try adjusting your search terms or filters to find what you're looking for.</p>
                </div>
            </div>
        </div>
    </div>
</section>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    // line 594
    /**
     * @return iterable<null|scalar|\Stringable>
     */
    public function block_javascripts(array $context, array $blocks = []): iterable
    {
        $macros = $this->macros;
        $__internal_5a27a8ba21ca79b61932376b2fa922d2 = $this->extensions["Symfony\\Bundle\\WebProfilerBundle\\Twig\\WebProfilerExtension"];
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->enter($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        $__internal_6f47bbe9983af81f1e7450e9a3e3768f = $this->extensions["Symfony\\Bridge\\Twig\\Extension\\ProfilerExtension"];
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->enter($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof = new \Twig\Profiler\Profile($this->getTemplateName(), "block", "javascripts"));

        // line 595
        yield "    ";
        yield from $this->yieldParentBlock("javascripts", $context, $blocks);
        yield "
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('video-search');
            const filterButtons = document.querySelectorAll('.filter-btn');
            const videoCards = document.querySelectorAll('.video-card');
            const videoGrid = document.getElementById('video-grid');
            const noResults = document.getElementById('no-results');
            const loadingSpinner = document.getElementById('loading-spinner');

            let currentFilter = 'all';
            let currentSearch = '';

            // Search functionality
            searchInput.addEventListener('input', function() {
                currentSearch = this.value.toLowerCase().trim();
                filterVideos();
            });

            // Filter functionality
            filterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Update active filter button
                    filterButtons.forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');

                    currentFilter = this.dataset.filter;
                    filterVideos();
                });
            });

            // Filter videos based on search and category
            function filterVideos() {
                showLoading();

                setTimeout(() => {
                    let visibleCount = 0;

                    videoCards.forEach(card => {
                        const title = card.dataset.title || '';
                        const description = card.dataset.description || '';
                        const category = card.dataset.category || '';
                        const accessLevel = card.dataset.accessLevel || '';

                        // Check search match
                        const searchMatch = currentSearch === '' ||
                            title.includes(currentSearch) ||
                            description.includes(currentSearch) ||
                            category.toLowerCase().includes(currentSearch);

                        // Check filter match
                        let filterMatch = true;
                        if (currentFilter !== 'all') {
                            if (['public_free', 'login_required_free', 'premium'].includes(currentFilter)) {
                                filterMatch = accessLevel === currentFilter;
                            } else {
                                filterMatch = category === currentFilter;
                            }
                        }

                        // Show/hide card
                        if (searchMatch && filterMatch) {
                            card.style.display = 'flex';
                            visibleCount++;
                        } else {
                            card.style.display = 'none';
                        }
                    });

                    // Show/hide no results message
                    if (visibleCount === 0) {
                        noResults.style.display = 'block';
                        videoGrid.style.display = 'none';
                    } else {
                        noResults.style.display = 'none';
                        videoGrid.style.display = 'grid';
                    }

                    hideLoading();
                }, 300); // Small delay for better UX
            }

            function showLoading() {
                loadingSpinner.style.display = 'block';
                videoGrid.style.opacity = '0.5';
            }

            function hideLoading() {
                loadingSpinner.style.display = 'none';
                videoGrid.style.opacity = '1';
            }

            // Video card click handlers
            videoCards.forEach(card => {
                const thumbnail = card.querySelector('.video-thumbnail');
                const playButton = card.querySelector('.play-button');

                if (thumbnail && playButton) {
                    thumbnail.addEventListener('click', function() {
                        const detailsButton = card.querySelector('.btn-details');

                        if (detailsButton) {
                            window.location.href = detailsButton.href;
                        }
                    });
                }
            });

            // Smooth animations for filter changes
            function animateCards() {
                videoCards.forEach((card, index) => {
                    if (card.style.display !== 'none') {
                        card.style.animation = `fadeInUp 0.6s ease forwards \${index * 0.1}s`;
                    }
                });
            }

            // Add CSS animation
            const style = document.createElement('style');
            style.textContent = `
                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(30px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }
            `;
            document.head.appendChild(style);

            // Initial animation
            animateCards();

            // Keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                // Focus search on Ctrl+F or Cmd+F
                if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
                    e.preventDefault();
                    searchInput.focus();
                }

                // Clear search on Escape
                if (e.key === 'Escape' && document.activeElement === searchInput) {
                    searchInput.value = '';
                    currentSearch = '';
                    filterVideos();
                }
            });

            // URL hash handling for direct links to filtered content
            function handleHashChange() {
                const hash = window.location.hash.substring(1);
                if (hash) {
                    const filterButton = document.querySelector(`[data-filter=\"\${hash}\"]`);
                    if (filterButton) {
                        filterButton.click();
                    }
                }
            }

            // Handle initial hash and hash changes
            handleHashChange();
            window.addEventListener('hashchange', handleHashChange);

            // Update URL hash when filter changes
            filterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const filter = this.dataset.filter;
                    if (filter !== 'all') {
                        window.history.replaceState(null, null, `#\${filter}`);
                    } else {
                        window.history.replaceState(null, null, window.location.pathname);
                    }
                });
            });

            // Performance optimization: Debounce search
            function debounce(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }

            // Replace immediate search with debounced version
            const debouncedFilter = debounce(filterVideos, 300);
            searchInput.removeEventListener('input', filterVideos);
            searchInput.addEventListener('input', function() {
                currentSearch = this.value.toLowerCase().trim();
                debouncedFilter();
            });
        });
    </script>
";
        
        $__internal_6f47bbe9983af81f1e7450e9a3e3768f->leave($__internal_6f47bbe9983af81f1e7450e9a3e3768f_prof);

        
        $__internal_5a27a8ba21ca79b61932376b2fa922d2->leave($__internal_5a27a8ba21ca79b61932376b2fa922d2_prof);

        yield from [];
    }

    /**
     * @codeCoverageIgnore
     */
    public function getTemplateName(): string
    {
        return "video/list.html.twig";
    }

    /**
     * @codeCoverageIgnore
     */
    public function isTraitable(): bool
    {
        return false;
    }

    /**
     * @codeCoverageIgnore
     */
    public function getDebugInfo(): array
    {
        return array (  844 => 595,  831 => 594,  808 => 580,  800 => 574,  797 => 573,  784 => 566,  779 => 563,  773 => 560,  770 => 559,  768 => 558,  760 => 553,  756 => 551,  750 => 549,  748 => 548,  741 => 544,  733 => 538,  729 => 536,  725 => 534,  723 => 533,  720 => 532,  718 => 531,  713 => 528,  707 => 525,  704 => 524,  698 => 520,  696 => 519,  691 => 516,  689 => 515,  676 => 504,  670 => 500,  664 => 497,  659 => 496,  657 => 495,  650 => 491,  646 => 490,  642 => 489,  638 => 488,  635 => 487,  630 => 486,  628 => 485,  573 => 432,  560 => 431,  126 => 8,  113 => 7,  90 => 5,  67 => 3,  44 => 1,);
    }

    public function getSourceContext(): Source
    {
        return new Source("{% extends 'base.html.twig' %}

{% block title %}Video Library - Capitol Academy{% endblock %}

{% block meta_description %}Explore our comprehensive video library featuring professional trading education content, market analysis, and expert insights from Capitol Academy.{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        /* Video Library Styles */
        .video-library-hero {
            background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%);
            padding: 120px 0 80px;
            position: relative;
        }

        .hero-content {
            text-align: center;
            color: white;
        }

        .hero-title {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 15px;
            font-family: 'Montserrat', sans-serif;
        }

        .hero-subtitle {
            font-size: 1.2rem;
            margin-bottom: 0;
            opacity: 0.9;
            font-family: 'Calibri', Arial, sans-serif;
        }

        .search-section {
            background: white;
            padding: 40px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .search-container {
            max-width: 600px;
            margin: 0 auto;
            position: relative;
        }

        .search-box {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 50px;
            padding: 15px 50px 15px 25px;
            font-size: 1rem;
            width: 100%;
            transition: all 0.3s ease;
        }

        .search-box:focus {
            outline: none;
            border-color: #011a2d;
            background: white;
            box-shadow: 0 0 0 3px rgba(1, 26, 45, 0.1);
        }

        .search-icon {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
            font-size: 1.1rem;
        }

        .filter-section {
            background: #f8f9fa;
            padding: 30px 0;
        }

        .filter-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            justify-content: center;
            margin-bottom: 20px;
        }

        .filter-btn {
            background: white;
            color: #6c757d;
            border: 2px solid #e9ecef;
            padding: 8px 20px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
            font-size: 0.9rem;
        }

        .filter-btn:hover,
        .filter-btn.active {
            background: #011a2d;
            border-color: #011a2d;
            color: white;
            transform: translateY(-1px);
        }

        .video-catalog-section {
            padding: 60px 0;
            background: white;
        }

        .section-title {
            text-align: center;
            margin-bottom: 40px;
        }

        .section-title h2 {
            font-family: 'Montserrat', sans-serif;
            font-size: 2.5rem;
            font-weight: 700;
            color: #011a2d;
            margin-bottom: 10px;
        }

        .section-title p {
            font-size: 1.1rem;
            color: #6c757d;
            margin: 0;
        }

        .video-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 25px;
            margin-top: 40px;
        }

        .video-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(1, 26, 45, 0.1);
            transition: all 0.3s ease;
            position: relative;
            height: 100%;
            display: flex;
            flex-direction: column;
            border: 1px solid #f1f3f4;
        }

        .video-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(1, 26, 45, 0.15);
            border-color: #e9ecef;
        }

        .video-thumbnail {
            position: relative;
            height: 180px;
            overflow: hidden;
            background: #f8f9fa;
        }

        .video-thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .video-card:hover .video-thumbnail img {
            transform: scale(1.03);
        }

        .video-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(1, 26, 45, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .video-card:hover .video-overlay {
            opacity: 1;
        }

        .play-button {
            width: 50px;
            height: 50px;
            background: #a90418;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            transition: all 0.3s ease;
        }

        .play-button:hover {
            background: #8b0314;
            transform: scale(1.1);
        }

        .video-badges {
            position: absolute;
            top: 12px;
            left: 12px;
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .video-badge {
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
            backdrop-filter: blur(10px);
        }

        .badge-free {
            background: rgba(40, 167, 69, 0.95);
            color: white;
        }

        .badge-premium {
            background: rgba(169, 4, 24, 0.95);
            color: white;
        }

        .badge-login-required {
            background: rgba(1, 26, 45, 0.95);
            color: white;
        }

        .video-content {
            padding: 20px;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }

        .video-title {
            color: #011a2d;
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 12px;
            line-height: 1.4;
            font-family: 'Montserrat', sans-serif;
            font-family: 'Montserrat', sans-serif;
        }

        .video-description {
            color: #6c757d;
            font-size: 0.95rem;
            line-height: 1.6;
            margin-bottom: 20px;
            flex-grow: 1;
        }

        .video-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            font-size: 0.9rem;
            color: #6c757d;
        }

        .video-category {
            background: #e9ecef;
            color: #495057;
            padding: 4px 12px;
            border-radius: 12px;
            font-weight: 600;
        }

        .video-date {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .video-actions {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .btn-watch {
            background: linear-gradient(135deg, #a90418, #8b0314);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            flex-grow: 1;
            text-align: center;
        }

        .btn-watch:hover {
            background: linear-gradient(135deg, #8b0314, #a90418);
            transform: translateY(-2px);
            color: white;
            text-decoration: none;
        }

        .btn-purchase {
            background: linear-gradient(135deg, #ffc107, #e0a800);
            color: #000;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            flex-grow: 1;
            text-align: center;
        }

        .btn-purchase:hover {
            background: linear-gradient(135deg, #e0a800, #ffc107);
            transform: translateY(-2px);
            color: #000;
            text-decoration: none;
        }

        .btn-login {
            background: linear-gradient(135deg, #011a2d, #1a3461);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            flex-grow: 1;
            text-align: center;
        }

        .btn-login:hover {
            background: linear-gradient(135deg, #1a3461, #011a2d);
            transform: translateY(-2px);
            color: white;
            text-decoration: none;
        }

        .btn-details {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.3s ease;
            flex-grow: 1;
            text-align: center;
            text-decoration: none;
        }

        .btn-details:hover {
            background: linear-gradient(135deg, #138496, #17a2b8);
            transform: translateY(-2px);
            color: white;
            text-decoration: none;
        }

        .no-videos {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .no-videos i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .loading-spinner {
            text-align: center;
            padding: 40px;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #a90418;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }

            .video-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .filter-buttons {
                justify-content: flex-start;
            }

            .search-filter-section {
                padding: 20px;
            }
        }
    </style>
{% endblock %}

{% block body %}
<!-- Video Library Hero Section -->
<section class=\"video-library-hero\">
    <div class=\"container\">
        <div class=\"hero-content\">
            <h1 class=\"hero-title\">Video Library</h1>
            <p class=\"hero-subtitle\">
                Explore our comprehensive collection of professional trading education videos,
                market analysis, and expert insights to enhance your trading skills.
            </p>
        </div>
    </div>
</section>

<!-- Search Section -->
<section class=\"search-section\">
    <div class=\"container\">
        <div class=\"search-container\">
            <input type=\"text\"
                   class=\"search-box\"
                   id=\"video-search\"
                   placeholder=\"Search videos by title, description, or category...\">
            <i class=\"fas fa-search search-icon\"></i>
        </div>
    </div>
</section>

<!-- Filter Section -->
<section class=\"filter-section\">
    <div class=\"container\">
        <div class=\"filter-buttons\">
            <button class=\"filter-btn active\" data-filter=\"all\">All Videos</button>
            <button class=\"filter-btn\" data-filter=\"free\">Free</button>
            <button class=\"filter-btn\" data-filter=\"premium\">Premium</button>
            <button class=\"filter-btn\" data-filter=\"youtube\">YouTube</button>
            <button class=\"filter-btn\" data-filter=\"vdocipher\">VdoCipher</button>
        </div>
    </div>
</section>
<!-- Video Catalog Section -->
<section class=\"video-catalog-section\">
    <div class=\"container\">
        <div class=\"section-title\">
            <h2>Our Video Collection</h2>
            <p>Professional trading education content from industry experts</p>
        </div>
                <!-- Loading Spinner -->
                <div class=\"loading-spinner\" id=\"loading-spinner\" style=\"display: none;\">
                    <div class=\"spinner\"></div>
                    <p>Loading videos...</p>
                </div>

                <!-- Video Grid -->
                <div class=\"video-grid\" id=\"video-grid\">
                    {% if videos|length > 0 %}
                        {% for video in videos %}
                            <div class=\"video-card\"
                                 data-category=\"{{ video.category }}\"
                                 data-access-level=\"{{ video.accessLevel }}\"
                                 data-title=\"{{ video.title|lower }}\"
                                 data-description=\"{{ video.description|lower }}\">

                                <!-- Video Thumbnail -->
                                <div class=\"video-thumbnail\">
                                    {% if video.thumbnail %}
                                        <img src=\"{{ asset('uploads/videos/thumbnails/' ~ video.thumbnail) }}\"
                                             alt=\"{{ video.title }}\"
                                             loading=\"lazy\">
                                    {% else %}
                                        <div style=\"width: 100%; height: 100%; background: linear-gradient(135deg, #f8f9fa, #e9ecef); display: flex; align-items: center; justify-content: center;\">
                                            <i class=\"fas fa-video text-muted\" style=\"font-size: 3rem;\"></i>
                                        </div>
                                    {% endif %}

                                    <!-- Video Overlay -->
                                    <div class=\"video-overlay\">
                                        <div class=\"play-button\">
                                            <i class=\"fas fa-play\"></i>
                                        </div>
                                    </div>

                                    <!-- Video Badges -->
                                    <div class=\"video-badges\">
                                        <!-- Access Level Badge -->
                                        {% if video.accessLevel == 'public_free' %}
                                            <span class=\"video-badge badge-free\">
                                                <i class=\"fas fa-globe\"></i> Free
                                            </span>
                                        {% elseif video.accessLevel == 'login_required_free' %}
                                            <span class=\"video-badge badge-login-required\">
                                                <i class=\"fas fa-user\"></i> Login Required
                                            </span>
                                        {% else %}
                                            <span class=\"video-badge badge-premium\">
                                                <i class=\"fas fa-crown\"></i> {{ video.formattedPrice }}
                                            </span>
                                        {% endif %}

                                        <!-- Video Source Badge -->
                                        <span class=\"video-badge badge-source\">
                                            {% if video.videoSourceType == 'youtube' %}
                                                <i class=\"fab fa-youtube\"></i> YouTube
                                            {% elseif video.videoSourceType == 'vdocipher' %}
                                                <i class=\"fas fa-shield-alt\"></i> VdoCipher
                                            {% else %}
                                                <i class=\"fas fa-play\"></i> Direct
                                            {% endif %}
                                        </span>
                                    </div>
                                </div>

                                <!-- Video Content -->
                                <div class=\"video-content\">
                                    <h3 class=\"video-title\">{{ video.title }}</h3>

                                    <!-- Video Meta -->
                                    <div class=\"video-meta\">
                                        {% if video.category %}
                                            <span class=\"video-category\">{{ video.category }}</span>
                                        {% endif %}
                                        <span class=\"video-date\">
                                            <i class=\"fas fa-calendar\"></i>
                                            {{ video.createdAt|date('M d, Y') }}
                                        </span>
                                    </div>

                                    <!-- Video Description -->
                                    {% if video.description %}
                                        <p class=\"video-description\">
                                            {{ video.description|length > 120 ? video.description|slice(0, 120) ~ '...' : video.description }}
                                        </p>
                                    {% endif %}

                                    <!-- Video Actions -->
                                    <div class=\"video-actions\">
                                        <a href=\"{{ path('app_video_show', {'slug': video.slug}) }}\" class=\"btn-details\">
                                            <i class=\"fas fa-info-circle\"></i> See Details
                                        </a>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    {% else %}
                        <div class=\"no-videos\">
                            <i class=\"fas fa-video\"></i>
                            <h3>No Videos Available</h3>
                            <p>We're working on adding more content. Please check back soon!</p>
                        </div>
                    {% endif %}
                </div>

                <!-- No Results Message -->
                <div class=\"no-videos\" id=\"no-results\" style=\"display: none;\">
                    <i class=\"fas fa-search\"></i>
                    <h3>No Videos Found</h3>
                    <p>Try adjusting your search terms or filters to find what you're looking for.</p>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('video-search');
            const filterButtons = document.querySelectorAll('.filter-btn');
            const videoCards = document.querySelectorAll('.video-card');
            const videoGrid = document.getElementById('video-grid');
            const noResults = document.getElementById('no-results');
            const loadingSpinner = document.getElementById('loading-spinner');

            let currentFilter = 'all';
            let currentSearch = '';

            // Search functionality
            searchInput.addEventListener('input', function() {
                currentSearch = this.value.toLowerCase().trim();
                filterVideos();
            });

            // Filter functionality
            filterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Update active filter button
                    filterButtons.forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');

                    currentFilter = this.dataset.filter;
                    filterVideos();
                });
            });

            // Filter videos based on search and category
            function filterVideos() {
                showLoading();

                setTimeout(() => {
                    let visibleCount = 0;

                    videoCards.forEach(card => {
                        const title = card.dataset.title || '';
                        const description = card.dataset.description || '';
                        const category = card.dataset.category || '';
                        const accessLevel = card.dataset.accessLevel || '';

                        // Check search match
                        const searchMatch = currentSearch === '' ||
                            title.includes(currentSearch) ||
                            description.includes(currentSearch) ||
                            category.toLowerCase().includes(currentSearch);

                        // Check filter match
                        let filterMatch = true;
                        if (currentFilter !== 'all') {
                            if (['public_free', 'login_required_free', 'premium'].includes(currentFilter)) {
                                filterMatch = accessLevel === currentFilter;
                            } else {
                                filterMatch = category === currentFilter;
                            }
                        }

                        // Show/hide card
                        if (searchMatch && filterMatch) {
                            card.style.display = 'flex';
                            visibleCount++;
                        } else {
                            card.style.display = 'none';
                        }
                    });

                    // Show/hide no results message
                    if (visibleCount === 0) {
                        noResults.style.display = 'block';
                        videoGrid.style.display = 'none';
                    } else {
                        noResults.style.display = 'none';
                        videoGrid.style.display = 'grid';
                    }

                    hideLoading();
                }, 300); // Small delay for better UX
            }

            function showLoading() {
                loadingSpinner.style.display = 'block';
                videoGrid.style.opacity = '0.5';
            }

            function hideLoading() {
                loadingSpinner.style.display = 'none';
                videoGrid.style.opacity = '1';
            }

            // Video card click handlers
            videoCards.forEach(card => {
                const thumbnail = card.querySelector('.video-thumbnail');
                const playButton = card.querySelector('.play-button');

                if (thumbnail && playButton) {
                    thumbnail.addEventListener('click', function() {
                        const detailsButton = card.querySelector('.btn-details');

                        if (detailsButton) {
                            window.location.href = detailsButton.href;
                        }
                    });
                }
            });

            // Smooth animations for filter changes
            function animateCards() {
                videoCards.forEach((card, index) => {
                    if (card.style.display !== 'none') {
                        card.style.animation = `fadeInUp 0.6s ease forwards \${index * 0.1}s`;
                    }
                });
            }

            // Add CSS animation
            const style = document.createElement('style');
            style.textContent = `
                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(30px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }
            `;
            document.head.appendChild(style);

            // Initial animation
            animateCards();

            // Keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                // Focus search on Ctrl+F or Cmd+F
                if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
                    e.preventDefault();
                    searchInput.focus();
                }

                // Clear search on Escape
                if (e.key === 'Escape' && document.activeElement === searchInput) {
                    searchInput.value = '';
                    currentSearch = '';
                    filterVideos();
                }
            });

            // URL hash handling for direct links to filtered content
            function handleHashChange() {
                const hash = window.location.hash.substring(1);
                if (hash) {
                    const filterButton = document.querySelector(`[data-filter=\"\${hash}\"]`);
                    if (filterButton) {
                        filterButton.click();
                    }
                }
            }

            // Handle initial hash and hash changes
            handleHashChange();
            window.addEventListener('hashchange', handleHashChange);

            // Update URL hash when filter changes
            filterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const filter = this.dataset.filter;
                    if (filter !== 'all') {
                        window.history.replaceState(null, null, `#\${filter}`);
                    } else {
                        window.history.replaceState(null, null, window.location.pathname);
                    }
                });
            });

            // Performance optimization: Debounce search
            function debounce(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }

            // Replace immediate search with debounced version
            const debouncedFilter = debounce(filterVideos, 300);
            searchInput.removeEventListener('input', filterVideos);
            searchInput.addEventListener('input', function() {
                currentSearch = this.value.toLowerCase().trim();
                debouncedFilter();
            });
        });
    </script>
{% endblock %}", "video/list.html.twig", "C:\\Users\\<USER>\\Desktop\\Capitol Academy\\Capitol-Academy\\templates\\video\\list.html.twig");
    }
}
