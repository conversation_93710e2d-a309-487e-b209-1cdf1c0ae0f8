{% extends 'base.html.twig' %}

{% block title %}{{ video.title }} - Capitol Academy{% endblock %}

{% block meta_description %}{{ video.description ? video.description|slice(0, 160) : 'Watch ' ~ video.title ~ ' on Capitol Academy - Professional Trading Education' }}{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        /* Capitol Academy Video Detail Page Styles */
        .video-detail-section {
            background: #f8f9fa;
            padding-top: 100px;
            padding-bottom: 60px;
            min-height: 100vh;
        }

        .video-main-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(1, 26, 45, 0.1);
            overflow: hidden;
            margin-bottom: 30px;
        }

        .video-player-section {
            background: #000;
            position: relative;
        }

        .video-player-wrapper {
            position: relative;
            width: 100%;
            background: #000;
        }

        .video-info-section {
            padding: 30px;
        }

        .video-player {
            width: 100%;
            aspect-ratio: 16/9;
            background: #000;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .video-player iframe,
        .video-player video {
            width: 100%;
            height: 100%;
            border: none;
        }

        .video-description {
            font-family: '<PERSON><PERSON>ri', Arial, sans-serif;
            font-size: 1.1rem;
            line-height: 1.6;
            color: #343a40;
            margin-bottom: 30px;
        }

        .video-title {
            color: #011a2d;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            font-family: 'Montserrat', sans-serif;
        }

        .video-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 25px;
            align-items: center;
        }

        .video-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .badge-free {
            background: #28a745;
            color: white;
        }

        .badge-premium {
            background: #a90418;
            color: white;
        }

        .badge-login-required {
            background: #011a2d;
            color: white;
        }

        .video-date {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .video-description {
            color: #343a40;
            font-size: 1.1rem;
            line-height: 1.8;
            margin-bottom: 30px;
        }

        .access-denied-card {
            background: linear-gradient(135deg, #a90418, #8b0314);
            color: white;
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            margin-top: 30px;
        }

        .access-denied-card h3 {
            font-size: 2rem;
            margin-bottom: 20px;
        }

        .purchase-button {
            background: linear-gradient(135deg, #a90418, #8b0314);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .purchase-button:hover {
            background: linear-gradient(135deg, #8b0314, #a90418);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(169, 4, 24, 0.3);
            color: white;
            text-decoration: none;
        }

        .login-button {
            background: linear-gradient(135deg, #011a2d, #1a3461);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .login-button:hover {
            background: linear-gradient(135deg, #1a3461, #011a2d);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(1, 26, 45, 0.3);
            color: white;
            text-decoration: none;
        }

        .premium-video-info {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 25px;
            margin-top: 20px;
            text-align: center;
        }

        .price-info {
            margin-bottom: 20px;
        }

        .price-display {
            font-size: 2rem;
            font-weight: 700;
            color: #a90418;
            margin-bottom: 8px;
            font-family: 'Montserrat', sans-serif;
        }

        .access-info {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1.1rem;
            margin-bottom: 0;
        }

        .add-to-cart-button {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-weight: 600;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            width: 100%;
            justify-content: center;
        }

        .add-to-cart-button:hover {
            background: linear-gradient(135deg, #20c997, #28a745);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(40, 167, 69, 0.3);
            color: white;
        }

        .add-to-cart-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .related-videos-section {
            background: #f8f9fa;
            padding: 60px 0;
        }

        .related-video-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            height: 100%;
        }

        .related-video-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .related-video-thumbnail {
            width: 100%;
            height: 200px;
            object-fit: cover;
            background: #f8f9fa;
        }

        .related-video-content {
            padding: 20px;
        }

        .related-video-title {
            color: #011a2d;
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 10px;
            line-height: 1.4;
        }

        .vdocipher-player {
            background: #000;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 500px;
            position: relative;
        }

        .vdocipher-loading {
            color: white;
            text-align: center;
        }

        .video-source-indicator {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        @media (max-width: 768px) {
            .video-hero-section {
                padding-top: 60px;
                min-height: auto;
            }

            .video-player {
                height: 300px;
            }

            .video-title {
                font-size: 2rem;
            }

            .video-meta {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }
    </style>
{% endblock %}

{% block body %}
<!-- Video Detail Section -->
<section class="video-detail-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <!-- Video Main Container -->
                <div class="video-main-container">
                    <!-- Video Player Section -->
                    <div class="video-player-section">
                        <div class="video-player-wrapper">
                            <div class="video-player">
                            {% if has_access %}
                                <!-- Video Source Indicator -->
                                <div class="video-source-indicator">
                                    {% if video.videoSourceType == 'youtube' %}
                                        <i class="fab fa-youtube"></i> YouTube
                                    {% elseif video.videoSourceType == 'vdocipher' %}
                                        <i class="fas fa-shield-alt"></i> VdoCipher
                                    {% else %}
                                        <i class="fas fa-play"></i> Direct
                                    {% endif %}
                                </div>

                                <!-- YouTube Video Player -->
                                {% if video.videoSourceType == 'youtube' and video.youtubeEmbedUrl %}
                                    <iframe src="{{ video.youtubeEmbedUrl }}"
                                            frameborder="0"
                                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                            allowfullscreen>
                                    </iframe>

                                <!-- VdoCipher Video Player -->
                                {% elseif video.videoSourceType == 'vdocipher' and video.vdocipherVideoId %}
                                    <div class="vdocipher-player" id="vdocipher-player">
                                        <div class="vdocipher-loading">
                                            <i class="fas fa-spinner fa-spin" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                                            <h4>Loading Secure Video...</h4>
                                            <p>Initializing DRM-protected playback</p>
                                        </div>
                                    </div>

                                <!-- Direct Upload Video Player -->
                                {% elseif video.videoFile %}
                                    <video controls preload="metadata" poster="{{ video.thumbnail ? asset('uploads/videos/thumbnails/' ~ video.thumbnail) : '' }}">
                                        <source src="{{ asset('uploads/videos/files/' ~ video.videoFile) }}" type="video/mp4">
                                        <p>Your browser doesn't support HTML5 video. <a href="{{ asset('uploads/videos/files/' ~ video.videoFile) }}">Download the video</a> instead.</p>
                                    </video>

                                <!-- No Video Source -->
                                {% else %}
                                    <div class="d-flex align-items-center justify-content-center h-100">
                                        <div class="text-center text-white">
                                            <i class="fas fa-exclamation-triangle" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                                            <h4>Video Not Available</h4>
                                            <p>This video is currently being processed. Please try again later.</p>
                                        </div>
                                    </div>
                                {% endif %}
                            {% else %}
                                <!-- Access Denied - Show Thumbnail with Overlay -->
                                <div class="d-flex align-items-center justify-content-center h-100" style="background: {{ video.thumbnail ? 'url(' ~ asset('uploads/videos/thumbnails/' ~ video.thumbnail) ~ ')' : '#000' }} center/cover;">
                                    <div style="background: rgba(0, 0, 0, 0.8); width: 100%; height: 100%; display: flex; align-items: center; justify-content: center;">
                                        <div class="text-center text-white">
                                            <i class="fas fa-lock" style="font-size: 4rem; margin-bottom: 1rem;"></i>
                                            <h3>Premium Content</h3>
                                            <p class="mb-0">This video requires access to view</p>
                                        </div>
                                    </div>
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    </div>

                    <!-- Video Information Section -->
                    <div class="video-info-section">
                        <h1 class="video-title">{{ video.title }}</h1>

                        <!-- Video Meta Information -->
                        <div class="video-meta">
                            <!-- Access Level Badge -->
                            {% if video.accessLevel == 'public_free' %}
                                <span class="video-badge badge-free">
                                    <i class="fas fa-globe"></i>
                                    Public Free
                                </span>
                            {% elseif video.accessLevel == 'login_required_free' %}
                                <span class="video-badge badge-login-required">
                                    <i class="fas fa-user"></i>
                                    Login Required
                                </span>
                            {% else %}
                                <span class="video-badge badge-premium">
                                    <i class="fas fa-crown"></i>
                                    Premium - {{ video.formattedPrice }}
                                    {% if video.accessDuration %}
                                        ({{ video.formattedAccessDuration }})
                                    {% endif %}
                                </span>
                            {% endif %}

                            <!-- Category Badge -->
                            {% if video.category %}
                                <span class="video-badge" style="background: #6c757d; color: white;">
                                    <i class="fas fa-tag"></i>
                                    {{ video.category }}
                                </span>
                            {% endif %}

                            <!-- Video Source Badge -->
                            <span class="video-badge" style="background: #17a2b8; color: white;">
                                {% if video.videoSourceType == 'youtube' %}
                                    <i class="fab fa-youtube"></i>
                                    YouTube
                                {% elseif video.videoSourceType == 'vdocipher' %}
                                    <i class="fas fa-shield-alt"></i>
                                    VdoCipher
                                {% else %}
                                    <i class="fas fa-upload"></i>
                                    Direct Upload
                                {% endif %}
                            </span>

                            <!-- Upload Date -->
                            <span class="video-date">
                                <i class="fas fa-calendar me-1"></i>
                                {{ video.createdAt|date('M d, Y') }}
                            </span>
                        </div>

                        <!-- Video Description -->
                        {% if video.description %}
                            <div class="video-description">
                                {{ video.description|nl2br }}
                            </div>
                        {% endif %}

                        <!-- Access Control Actions -->
                        {% if not has_access %}
                            {% if video.accessLevel == 'login_required_free' and not app.user %}
                                <!-- Login Required for Free Video -->
                                <div class="text-center">
                                    <p class="mb-3">Please log in to watch this free video.</p>
                                    <a href="{{ path('app_login') }}" class="login-button">
                                        <i class="fas fa-sign-in-alt"></i>
                                        Login to Watch
                                    </a>
                                </div>
                            {% elseif video.accessLevel == 'premium' %}
                                <!-- Premium Video Purchase -->
                                <div class="text-center">
                                    <div class="premium-video-info">
                                        <div class="price-info mb-3">
                                            <h4 class="price-display">{{ video.formattedPrice }}</h4>
                                            <p class="access-info">
                                                {% if video.accessDuration %}
                                                    {{ video.accessDuration }} days access
                                                {% else %}
                                                    Lifetime access
                                                {% endif %}
                                            </p>
                                        </div>
                                        <button class="add-to-cart-button" onclick="addToCart('video', {{ video.id }})">
                                            <i class="fas fa-cart-plus"></i>
                                            Add to Cart
                                        </button>
                                        {% if not app.user %}
                                            <p class="mt-3 text-muted small">
                                                <i class="fas fa-info-circle me-1"></i>
                                                You'll be asked to login during checkout
                                            </p>
                                        {% endif %}
                                    </div>
                                </div>
                            {% endif %}
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Videos Section -->
{% if related_videos|length > 0 %}
<section class="related-videos-section">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h2 class="text-center mb-5" style="color: #011a2d; font-family: 'Montserrat', sans-serif; font-weight: 700;">
                    Related Videos
                </h2>
            </div>
        </div>
        <div class="row g-4">
            {% for related_video in related_videos %}
                <div class="col-lg-3 col-md-6">
                    <div class="related-video-card">
                        <a href="{{ path('app_video_show', {'id': related_video.id}) }}" class="text-decoration-none">
                            {% if related_video.thumbnail %}
                                <img src="{{ asset('uploads/videos/thumbnails/' ~ related_video.thumbnail) }}"
                                     alt="{{ related_video.title }}"
                                     class="related-video-thumbnail">
                            {% else %}
                                <div class="related-video-thumbnail d-flex align-items-center justify-content-center" style="background: #f8f9fa;">
                                    <i class="fas fa-video text-muted" style="font-size: 3rem;"></i>
                                </div>
                            {% endif %}
                            <div class="related-video-content">
                                <h5 class="related-video-title">{{ related_video.title }}</h5>
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>
                                        {{ related_video.createdAt|date('M d, Y') }}
                                    </small>
                                    {% if related_video.accessLevel == 'public_free' %}
                                        <span class="badge bg-success">Free</span>
                                    {% elseif related_video.accessLevel == 'login_required_free' %}
                                        <span class="badge bg-info">Login Required</span>
                                    {% else %}
                                        <span class="badge bg-warning text-dark">{{ related_video.formattedPrice }}</span>
                                    {% endif %}
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>
</section>
{% endif %}
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        // VdoCipher Player Initialization
        {% if has_access and video.videoSourceType == 'vdocipher' and video.vdocipherVideoId %}
        document.addEventListener('DOMContentLoaded', function() {
            initializeVdoCipherPlayer();
        });

        function initializeVdoCipherPlayer() {
            // Get video access token from server
            fetch('{{ path('app_video_vdocipher_token', {'id': video.id}) }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.otp && data.playbackInfo) {
                    // Initialize VdoCipher player
                    new VdoPlayer({
                        otp: data.otp,
                        playbackInfo: data.playbackInfo,
                        theme: "9bb535",
                        container: document.querySelector("#vdocipher-player"),
                        width: "100%",
                        height: "100%"
                    });
                } else {
                    showVdoCipherError('Failed to load video. Please try again.');
                }
            })
            .catch(error => {
                console.error('VdoCipher initialization error:', error);
                showVdoCipherError('Unable to load video player. Please refresh the page.');
            });
        }

        function showVdoCipherError(message) {
            const player = document.getElementById('vdocipher-player');
            player.innerHTML = `
                <div class="vdocipher-loading">
                    <i class="fas fa-exclamation-triangle text-warning" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                    <h4>Video Loading Error</h4>
                    <p>${message}</p>
                    <button class="btn btn-primary mt-3" onclick="initializeVdoCipherPlayer()">
                        <i class="fas fa-redo"></i> Retry
                    </button>
                </div>
            `;
        }
        {% endif %}

        // Video Purchase Function
        function purchaseVideo(videoId) {
            // Show loading state
            const button = event.target.closest('.purchase-button');
            const originalContent = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
            button.disabled = true;

            // Create Stripe checkout session
            fetch('{{ path('app_video_purchase') }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    video_id: videoId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.checkout_url) {
                    // Redirect to Stripe checkout
                    window.location.href = data.checkout_url;
                } else {
                    // Show error message
                    alert(data.message || 'Unable to process purchase. Please try again.');
                    button.innerHTML = originalContent;
                    button.disabled = false;
                }
            })
            .catch(error => {
                console.error('Purchase error:', error);
                alert('Unable to process purchase. Please try again.');
                button.innerHTML = originalContent;
                button.disabled = false;
            });
        }

        // Video Analytics Tracking
        {% if has_access %}
        document.addEventListener('DOMContentLoaded', function() {
            // Track video view
            fetch('{{ path('app_video_track_view', {'slug': video.slug}) }}', {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            // Track video engagement
            let watchTime = 0;
            const trackingInterval = setInterval(() => {
                watchTime += 5; // Track every 5 seconds

                // Send tracking data every 30 seconds
                if (watchTime % 30 === 0) {
                    fetch('{{ path('app_video_track_engagement', {'slug': video.slug}) }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: JSON.stringify({
                            watch_time: watchTime
                        })
                    });
                }
            }, 5000);

            // Stop tracking when user leaves page
            window.addEventListener('beforeunload', () => {
                clearInterval(trackingInterval);
            });
        });
        {% endif %}

        // Smooth scroll for related videos
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add to Cart Function
        function addToCart(type, id) {
            const button = event.target.closest('.add-to-cart-button');
            const originalContent = button.innerHTML;

            // Show loading state
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Adding...';
            button.disabled = true;

            fetch('{{ path('app_cart_add') }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: `type=${type}&id=${id}&quantity=1`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    button.innerHTML = '<i class="fas fa-check"></i> Added to Cart!';
                    button.style.background = 'linear-gradient(135deg, #28a745, #20c997)';

                    // Update cart display
                    if (typeof updateCartBadge === 'function') {
                        updateCartBadge(data.cart.item_count);
                    }
                    if (typeof loadCartContents === 'function') {
                        loadCartContents();
                    }

                    // Reset button after 2 seconds
                    setTimeout(() => {
                        button.innerHTML = originalContent;
                        button.disabled = false;
                        button.style.background = '';
                    }, 2000);
                } else {
                    // Show error message
                    alert(data.message || 'Unable to add item to cart. Please try again.');
                    button.innerHTML = originalContent;
                    button.disabled = false;
                }
            })
            .catch(error => {
                console.error('Add to cart error:', error);
                alert('Unable to add item to cart. Please try again.');
                button.innerHTML = originalContent;
                button.disabled = false;
            });
        }
    </script>

    <!-- VdoCipher Player Script -->
    {% if has_access and video.videoSourceType == 'vdocipher' %}
    <script src="https://player.vdocipher.com/v2/api.js"></script>
    {% endif %}
{% endblock %}