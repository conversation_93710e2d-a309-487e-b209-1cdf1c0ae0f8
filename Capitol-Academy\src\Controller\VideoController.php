<?php

namespace App\Controller;

use App\Entity\Video;
use App\Entity\User;
use App\Entity\UserVideoAccess;
use App\Repository\VideoRepository;
use App\Repository\UserVideoAccessRepository;
use App\Service\AccessControlService;
use App\Service\StripeService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;

#[Route('/videos')]
class VideoController extends AbstractController
{
    #[Route('/', name: 'app_videos_list')]
    #[Route('/', name: 'app_videos')]
    public function list(VideoRepository $videoRepository): Response
    {
        $videos = $videoRepository->findActiveVideos();

        return $this->render('video/list.html.twig', [
            'videos' => $videos,
        ]);
    }

    #[Route('/{slug}', name: 'app_video_show')]
    public function show(string $slug, VideoRepository $videoRepository, EntityManagerInterface $entityManager, AccessControlService $accessControlService): Response
    {
        $video = $videoRepository->findBySlugOrThrow($slug);

        // Check if user is logged in
        $user = $this->getUser();
        
        // For premium videos, require login
        if (!$video->isFree() && !$user) {
            // Redirect to login page for premium content
            $this->addFlash('info', 'Please log in to access premium video content.');
            return $this->redirectToRoute('app_login');
        }

        // Check access for premium videos
        $hasAccess = false;
        if ($video->isFree()) {
            $hasAccess = true;
        } elseif ($user instanceof User) {
            $hasAccess = $accessControlService->hasVideoAccess($user, $video);
        }

        // Get related videos
        $relatedVideos = [];
        if ($video->getCategory()) {
            $relatedVideos = $videoRepository->findByCategory($video->getCategory(), 4);
            $relatedVideos = array_filter($relatedVideos, fn($v) => $v->getId() !== $video->getId());
        }

        return $this->render('video/show.html.twig', [
            'video' => $video,
            'has_access' => $hasAccess,
            'related_videos' => $relatedVideos,
        ]);
    }

    #[Route('/{slug}/vdocipher-token', name: 'app_video_vdocipher_token', methods: ['POST'])]
    #[IsGranted('ROLE_USER')]
    public function getVdoCipherToken(string $slug, VideoRepository $videoRepository, AccessControlService $accessControlService): JsonResponse
    {
        try {
            $video = $videoRepository->findBySlugOrThrow($slug);
        } catch (\Exception $e) {
            return new JsonResponse(['success' => false, 'message' => 'Video not found'], 404);
        }

        // Check if user has access to this video
        $user = $this->getUser();
        if (!$user instanceof User || !$accessControlService->hasVideoAccess($user, $video)) {
            return new JsonResponse(['success' => false, 'message' => 'Access denied'], 403);
        }

        // Generate VdoCipher playback token
        $tokenData = $accessControlService->generateVideoPlaybackToken($user, $video);

        if (!$tokenData) {
            return new JsonResponse(['success' => false, 'message' => 'Unable to generate playback token'], 500);
        }

        return new JsonResponse([
            'success' => true,
            'otp' => $tokenData['otp'] ?? null,
            'playbackInfo' => $tokenData['playback_info'] ?? $video->getVdocipherVideoId(),
            'type' => $tokenData['type'] ?? 'premium'
        ]);
    }

    #[Route('/purchase', name: 'app_video_purchase', methods: ['POST'])]
    #[IsGranted('ROLE_USER')]
    public function purchaseVideo(Request $request, VideoRepository $videoRepository, StripeService $stripeService): JsonResponse
    {
        $data = json_decode($request->getContent(), true);
        $videoId = $data['video_id'] ?? null;

        if (!$videoId) {
            return new JsonResponse(['success' => false, 'message' => 'Video ID is required'], 400);
        }

        $video = $videoRepository->find($videoId);
        if (!$video) {
            return new JsonResponse(['success' => false, 'message' => 'Video not found'], 404);
        }

        // Check if video is premium
        if ($video->getAccessLevel() !== 'premium') {
            return new JsonResponse(['success' => false, 'message' => 'This video is not available for purchase'], 400);
        }

        $user = $this->getUser();
        if (!$user instanceof User) {
            return new JsonResponse(['success' => false, 'message' => 'User not authenticated'], 401);
        }

        try {
            // Create Stripe checkout session
            $session = $stripeService->createVideoCheckoutSession($video, $user);

            return new JsonResponse([
                'success' => true,
                'checkout_url' => $session->url,
                'session_id' => $session->id
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Unable to create checkout session: ' . $e->getMessage()
            ], 500);
        }
    }

    #[Route('/{slug}/track-view', name: 'app_video_track_view', methods: ['POST'])]
    public function trackVideoView(string $slug, VideoRepository $videoRepository, UserVideoAccessRepository $userVideoAccessRepository, EntityManagerInterface $entityManager): JsonResponse
    {
        try {
            $video = $videoRepository->findBySlugOrThrow($slug);
        } catch (\Exception $e) {
            return new JsonResponse(['success' => false], 404);
        }

        $user = $this->getUser();

        // Track view for both authenticated and anonymous users
        if ($user instanceof User) {
            // For authenticated users, update or create video access record
            $videoAccess = $userVideoAccessRepository->getUserVideoAccess($user, $video);

            if ($videoAccess) {
                $videoAccess->incrementAccessCount();
                $videoAccess->setLastAccessedAt(new \DateTimeImmutable());
                $entityManager->flush();
            } else {
                // For free videos, create tracking record
                if ($video->isFreeVideo()) {
                    $videoAccess = new UserVideoAccess();
                    $videoAccess->setUser($user);
                    $videoAccess->setVideo($video);
                    $videoAccess->setAccessCount(1);
                    $videoAccess->setLastAccessedAt(new \DateTimeImmutable());
                    $videoAccess->setExpiresAt(null); // Unlimited access for free videos
                    $videoAccess->setAccessSource('free_view');

                    $entityManager->persist($videoAccess);
                    $entityManager->flush();
                }
            }
        }

        return new JsonResponse(['success' => true]);
    }

    #[Route('/{slug}/track-engagement', name: 'app_video_track_engagement', methods: ['POST'])]
    #[IsGranted('ROLE_USER')]
    public function trackVideoEngagement(string $slug, Request $request, VideoRepository $videoRepository, UserVideoAccessRepository $userVideoAccessRepository, EntityManagerInterface $entityManager): JsonResponse
    {
        try {
            $video = $videoRepository->findBySlugOrThrow($slug);
        } catch (\Exception $e) {
            return new JsonResponse(['success' => false], 404);
        }

        $user = $this->getUser();
        $data = json_decode($request->getContent(), true);
        $watchTime = $data['watch_time'] ?? 0;

        // Update watch time for authenticated users
        $videoAccess = $userVideoAccessRepository->getUserVideoAccess($user, $video);

        if ($videoAccess) {
            // Note: UserVideoAccess doesn't have setWatchTime method, so we'll track via access count
            $videoAccess->setLastAccessedAt(new \DateTimeImmutable());
            $entityManager->flush();
        }

        return new JsonResponse(['success' => true]);
    }
}
